"""
Analysis Routes - US-06 & US-07
===============================

This module handles matching score calculation and suggestions.
Implements US-06 (Matching Score) and US-07 (Suggestions) functionality.

Routes:
- POST /api/calculate_match - Calculate matching score between resume and JD
- GET /api/matching_scores - List user's matching scores
- GET /api/matching_scores/<id> - Get matching score details
- GET /api/suggestions/<resume_id>/<jd_id> - Get basic suggestions
- POST /api/premium_suggestions - Generate premium AI suggestions
"""

import json
from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from models import db, Resume, JobDescription, MatchingScore, Suggestion, Keyword
from services.matching_engine import MatchingEngine
from services.suggestion_engine import SuggestionEngine

# Create Blueprint
analysis_bp = Blueprint('analysis', __name__, url_prefix='/api')

@analysis_bp.route('/calculate_match', methods=['POST'])
@jwt_required()
def calculate_match():
    """
    Calculate Matching Score Endpoint - US-06
    
    Expected JSON payload:
    {
        "resume_id": "uuid-here",
        "job_description_id": "uuid-here",
        "calculation_method": "jaccard"  // optional: jaccard, weighted, hybrid
    }
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Extract required fields
        resume_id = data.get('resume_id', '').strip()
        job_description_id = data.get('job_description_id', '').strip()
        calculation_method = data.get('calculation_method', 'jaccard').strip()
        
        # Validate required fields
        if not resume_id or not job_description_id:
            return jsonify({
                'success': False,
                'message': 'Resume ID and Job Description ID are required'
            }), 400
        
        # Validate calculation method
        valid_methods = ['jaccard', 'weighted', 'hybrid']
        if calculation_method not in valid_methods:
            return jsonify({
                'success': False,
                'message': f'Invalid calculation method. Must be one of: {", ".join(valid_methods)}'
            }), 400
        
        # Find resume
        resume = Resume.query.filter_by(
            id=resume_id,
            user_id=current_user_id,
            is_active=True
        ).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        # Find job description
        job_description = JobDescription.query.filter_by(
            id=job_description_id,
            user_id=current_user_id
        ).first()
        
        if not job_description:
            return jsonify({
                'success': False,
                'message': 'Job description not found'
            }), 404
        
        # Check if both have extracted text
        if not resume.extracted_text:
            return jsonify({
                'success': False,
                'message': 'Resume text not extracted. Please reprocess the resume.'
            }), 400
        
        if not job_description.job_description_text:
            return jsonify({
                'success': False,
                'message': 'Job description text is empty'
            }), 400
        
        # Calculate matching score
        matching_engine = MatchingEngine()
        match_result = matching_engine.calculate_match(
            resume_text=resume.extracted_text,
            job_description_text=job_description.job_description_text,
            resume_id=resume_id,
            job_description_id=job_description_id,
            method=calculation_method
        )
        
        if not match_result:
            return jsonify({
                'success': False,
                'message': 'Failed to calculate matching score'
            }), 500
        
        # Mark any existing matching scores as not current
        MatchingScore.query.filter_by(
            user_id=current_user_id,
            resume_id=resume_id,
            job_description_id=job_description_id
        ).update({'is_current': False})
        
        # Create new matching score record
        matching_score = MatchingScore(
            user_id=current_user_id,
            resume_id=resume_id,
            job_description_id=job_description_id,
            overall_match_percentage=match_result['overall_match_percentage'],
            jaccard_similarity=match_result['jaccard_similarity'],
            skill_match_percentage=match_result.get('skill_match_percentage', 0.0),
            experience_match_percentage=match_result.get('experience_match_percentage', 0.0),
            education_match_percentage=match_result.get('education_match_percentage', 0.0),
            keyword_overlap_count=match_result['keyword_overlap_count'],
            resume_keyword_count=match_result['resume_keyword_count'],
            jd_keyword_count=match_result['jd_keyword_count'],
            matched_keywords=json.dumps(match_result['matched_keywords']),
            missing_keywords=json.dumps(match_result['missing_keywords']),
            extra_keywords=json.dumps(match_result.get('extra_keywords', [])),
            calculation_method=calculation_method,
            confidence_score=match_result.get('confidence_score', 0.8),
            is_current=True
        )
        
        db.session.add(matching_score)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Matching score calculated successfully',
            'matching_score': matching_score.to_dict(),
            'match_details': match_result
        }), 201
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to calculate match: {str(e)}'
        }), 500

@analysis_bp.route('/matching_scores', methods=['GET'])
@jwt_required()
def list_matching_scores():
    """
    List User's Matching Scores Endpoint - US-06
    
    Returns all matching scores for the current user
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        current_only = request.args.get('current_only', 'true').lower() == 'true'
        
        # Query matching scores
        query = MatchingScore.query.filter_by(user_id=current_user_id)
        
        if current_only:
            query = query.filter_by(is_current=True)
        
        query = query.order_by(MatchingScore.created_at.desc())
        
        # Paginate results
        scores_paginated = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        return jsonify({
            'success': True,
            'matching_scores': [score.to_dict() for score in scores_paginated.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': scores_paginated.total,
                'pages': scores_paginated.pages,
                'has_next': scores_paginated.has_next,
                'has_prev': scores_paginated.has_prev
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to list matching scores: {str(e)}'
        }), 500

@analysis_bp.route('/matching_scores/<score_id>', methods=['GET'])
@jwt_required()
def get_matching_score_details(score_id):
    """
    Get Matching Score Details Endpoint - US-06
    
    Returns detailed information about a specific matching score
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find matching score
        matching_score = MatchingScore.query.filter_by(
            id=score_id,
            user_id=current_user_id
        ).first()
        
        if not matching_score:
            return jsonify({
                'success': False,
                'message': 'Matching score not found'
            }), 404
        
        # Get related resume and job description info
        resume = Resume.query.get(matching_score.resume_id)
        job_description = JobDescription.query.get(matching_score.job_description_id)
        
        score_data = matching_score.to_dict()
        
        if resume:
            score_data['resume_info'] = {
                'id': resume.id,
                'title': resume.resume_title,
                'filename': resume.original_filename,
                'created_at': resume.created_at.isoformat() if resume.created_at else None
            }
        
        if job_description:
            score_data['job_description_info'] = {
                'id': job_description.id,
                'title': job_description.title,
                'company_name': job_description.company_name,
                'location': job_description.location,
                'created_at': job_description.created_at.isoformat() if job_description.created_at else None
            }
        
        return jsonify({
            'success': True,
            'matching_score': score_data
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get matching score details: {str(e)}'
        }), 500

@analysis_bp.route('/suggestions/<resume_id>/<jd_id>', methods=['GET'])
@jwt_required()
def get_basic_suggestions(resume_id, jd_id):
    """
    Get Basic Suggestions Endpoint - US-07
    
    Returns basic suggestions for improving resume match with job description
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find the latest matching score for this resume-JD pair
        matching_score = MatchingScore.query.filter_by(
            user_id=current_user_id,
            resume_id=resume_id,
            job_description_id=jd_id,
            is_current=True
        ).first()
        
        if not matching_score:
            return jsonify({
                'success': False,
                'message': 'No matching score found. Please calculate match first.'
            }), 404
        
        # Generate basic suggestions
        suggestion_engine = SuggestionEngine()
        suggestions = suggestion_engine.generate_basic_suggestions(matching_score)
        
        # Save suggestions to database
        saved_suggestions = []
        for suggestion_data in suggestions:
            suggestion = Suggestion(
                user_id=current_user_id,
                matching_score_id=matching_score.id,
                suggestion_type='basic',
                category=suggestion_data['category'],
                title=suggestion_data['title'],
                description=suggestion_data['description'],
                priority=suggestion_data.get('priority', 'medium'),
                implementation_steps=json.dumps(suggestion_data.get('implementation_steps', [])),
                examples=json.dumps(suggestion_data.get('examples', [])),
                ai_generated=False
            )
            
            db.session.add(suggestion)
            saved_suggestions.append(suggestion)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Basic suggestions generated successfully',
            'suggestions': [s.to_dict() for s in saved_suggestions],
            'matching_score_id': matching_score.id
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to generate suggestions: {str(e)}'
        }), 500

@analysis_bp.route('/premium_suggestions', methods=['POST'])
@jwt_required()
def generate_premium_suggestions():
    """
    Generate Premium AI Suggestions Endpoint - US-07
    
    Expected JSON payload:
    {
        "matching_score_id": "uuid-here",
        "focus_areas": ["keywords", "skills", "experience"],  // optional
        "detail_level": "detailed"  // optional: basic, detailed, comprehensive
    }
    """
    try:
        # Get current user and claims
        current_user_id = get_jwt_identity()
        user_claims = get_jwt()
        
        # Check premium access
        if not user_claims.get('is_premium', False):
            return jsonify({
                'success': False,
                'message': 'Premium access required for AI-powered suggestions',
                'upgrade_required': True
            }), 403
        
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Extract required fields
        matching_score_id = data.get('matching_score_id', '').strip()
        focus_areas = data.get('focus_areas', ['keywords', 'skills', 'experience'])
        detail_level = data.get('detail_level', 'detailed')
        
        # Validate required fields
        if not matching_score_id:
            return jsonify({
                'success': False,
                'message': 'Matching score ID is required'
            }), 400
        
        # Find matching score
        matching_score = MatchingScore.query.filter_by(
            id=matching_score_id,
            user_id=current_user_id
        ).first()
        
        if not matching_score:
            return jsonify({
                'success': False,
                'message': 'Matching score not found'
            }), 404
        
        # Generate premium suggestions using AI
        suggestion_engine = SuggestionEngine()
        ai_suggestions = suggestion_engine.generate_premium_suggestions(
            matching_score=matching_score,
            focus_areas=focus_areas,
            detail_level=detail_level
        )
        
        if not ai_suggestions:
            return jsonify({
                'success': False,
                'message': 'Failed to generate AI suggestions. Please try again.'
            }), 500
        
        # Save premium suggestions to database
        saved_suggestions = []
        total_tokens = 0
        total_cost = 0.0
        
        for suggestion_data in ai_suggestions:
            suggestion = Suggestion(
                user_id=current_user_id,
                matching_score_id=matching_score.id,
                suggestion_type='premium',
                category=suggestion_data['category'],
                title=suggestion_data['title'],
                description=suggestion_data['description'],
                priority=suggestion_data.get('priority', 'medium'),
                implementation_steps=json.dumps(suggestion_data.get('implementation_steps', [])),
                examples=json.dumps(suggestion_data.get('examples', [])),
                ai_generated=True,
                ai_model_used=suggestion_data.get('ai_model_used', 'gpt-3.5-turbo'),
                ai_tokens_used=suggestion_data.get('tokens_used', 0),
                ai_cost=suggestion_data.get('cost', 0.0)
            )
            
            db.session.add(suggestion)
            saved_suggestions.append(suggestion)
            
            total_tokens += suggestion_data.get('tokens_used', 0)
            total_cost += suggestion_data.get('cost', 0.0)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Premium AI suggestions generated successfully',
            'suggestions': [s.to_dict() for s in saved_suggestions],
            'matching_score_id': matching_score.id,
            'usage_info': {
                'total_tokens_used': total_tokens,
                'total_cost': total_cost,
                'ai_model': 'gpt-3.5-turbo'
            }
        }), 201
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to generate premium suggestions: {str(e)}'
        }), 500

@analysis_bp.route('/suggestions/<suggestion_id>/status', methods=['PUT'])
@jwt_required()
def update_suggestion_status(suggestion_id):
    """
    Update Suggestion Status Endpoint - US-07
    
    Expected JSON payload:
    {
        "status": "implemented",  // pending, implemented, dismissed, saved
        "user_rating": 5,  // optional: 1-5 stars
        "user_feedback": "Very helpful suggestion"  // optional
    }
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find suggestion
        suggestion = Suggestion.query.filter_by(
            id=suggestion_id,
            user_id=current_user_id
        ).first()
        
        if not suggestion:
            return jsonify({
                'success': False,
                'message': 'Suggestion not found'
            }), 404
        
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Update status
        status = data.get('status', '').strip()
        valid_statuses = ['pending', 'implemented', 'dismissed', 'saved']
        
        if status and status in valid_statuses:
            suggestion.status = status
            if status == 'implemented':
                suggestion.implemented_at = datetime.utcnow()
        
        # Update rating
        user_rating = data.get('user_rating')
        if user_rating is not None:
            if isinstance(user_rating, int) and 1 <= user_rating <= 5:
                suggestion.user_rating = user_rating
            else:
                return jsonify({
                    'success': False,
                    'message': 'User rating must be an integer between 1 and 5'
                }), 400
        
        # Update feedback
        user_feedback = data.get('user_feedback')
        if user_feedback is not None:
            suggestion.user_feedback = user_feedback.strip()
        
        # Update timestamp
        suggestion.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Suggestion status updated successfully',
            'suggestion': suggestion.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to update suggestion status: {str(e)}'
        }), 500
