"""
Dr. Resume - Integrated Database Models
======================================

This file contains all database models for the integrated Dr. Resume application,
combining functionality from US-01 through US-10.

Models included:
- User (US-01): User registration and authentication
- Resume (US-03): Resume file storage and metadata
- JobDescription (US-04): Job description storage
- Keyword (US-05): Extracted keywords from resumes and job descriptions
- MatchingScore (US-06): Resume-JD matching calculations
- Suggestion (US-07): Basic and premium suggestions
- ScanHistory (US-08): Dashboard and history tracking
- UserProfile (US-10): Extended user profile information

Tech Stack: Flask-SQLAlchemy, SQLite (configurable to PostgreSQL)
"""

import uuid
import json
from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy.orm import relationship
from sqlalchemy import Column, String, Integer, Text, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Float, JSON

# Initialize SQLAlchemy
db = SQLAlchemy()

class User(db.Model):
    """
    User Model - US-01 User Registration
    
    Core user authentication and profile information
    """
    __tablename__ = 'users'
    
    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Authentication
    email = Column(String(120), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    
    # Profile
    first_name = Column(String(50))
    last_name = Column(String(50))
    
    # Account management
    is_active = Column(Boolean, default=True, nullable=False)
    is_premium = Column(Boolean, default=False, nullable=False)
    role = Column(String(20), default='user', nullable=False)  # user, premium, admin
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = Column(DateTime)
    
    # Relationships
    resumes = relationship("Resume", back_populates="user", cascade="all, delete-orphan")
    job_descriptions = relationship("JobDescription", back_populates="user", cascade="all, delete-orphan")
    keywords = relationship("Keyword", back_populates="user", cascade="all, delete-orphan")
    matching_scores = relationship("MatchingScore", back_populates="user", cascade="all, delete-orphan")
    suggestions = relationship("Suggestion", back_populates="user", cascade="all, delete-orphan")
    scan_history = relationship("ScanHistory", back_populates="user", cascade="all, delete-orphan")
    user_profile = relationship("UserProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    
    def __init__(self, email, password, first_name=None, last_name=None):
        self.email = email.lower().strip()
        self.password_hash = generate_password_hash(password)
        self.first_name = first_name
        self.last_name = last_name
    
    def check_password(self, password):
        """Check if provided password matches the hash"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        """Convert user to dictionary for JSON responses"""
        return {
            'id': self.id,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'is_active': self.is_active,
            'is_premium': self.is_premium,
            'role': self.role,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
    
    @classmethod
    def create_user(cls, email, password, first_name=None, last_name=None):
        """
        Create a new user with validation
        
        Returns:
            tuple: (user_instance, error_message)
        """
        try:
            # Check if email already exists
            existing_user = cls.query.filter_by(email=email.lower().strip()).first()
            if existing_user:
                return None, "Email already registered"
            
            # Create new user
            user = cls(email=email, password=password, first_name=first_name, last_name=last_name)
            db.session.add(user)
            db.session.commit()
            
            return user, None
            
        except Exception as e:
            db.session.rollback()
            return None, f"Registration failed: {str(e)}"

class Resume(db.Model):
    """
    Resume Model - US-03 Resume Upload
    
    Stores uploaded resume files and extracted content
    """
    __tablename__ = 'resumes'
    
    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # File information
    original_filename = Column(String(255), nullable=False)
    stored_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_type = Column(String(50), nullable=False)
    file_extension = Column(String(10), nullable=False)
    
    # Extracted content
    extracted_text = Column(Text)
    extraction_status = Column(String(20), default='pending')  # pending, success, failed
    extraction_error = Column(Text)
    
    # Metadata
    resume_title = Column(String(200))
    resume_description = Column(Text)
    
    # Status
    upload_status = Column(String(20), default='uploaded')  # uploaded, processing, processed
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="resumes")
    keywords = relationship("Keyword", back_populates="resume", cascade="all, delete-orphan")
    matching_scores = relationship("MatchingScore", back_populates="resume", cascade="all, delete-orphan")
    
    def to_dict(self):
        """Convert resume to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'file_extension': self.file_extension,
            'resume_title': self.resume_title,
            'resume_description': self.resume_description,
            'extraction_status': self.extraction_status,
            'upload_status': self.upload_status,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class JobDescription(db.Model):
    """
    Job Description Model - US-04 JD Upload
    
    Stores job descriptions and related metadata
    """
    __tablename__ = 'job_descriptions'
    
    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Foreign key
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # Job information
    title = Column(String(200), nullable=False)
    company_name = Column(String(100))
    job_description_text = Column(Text, nullable=False)
    location = Column(String(100))
    employment_type = Column(String(50), default='full-time')
    experience_level = Column(String(50))
    salary_range = Column(String(100))
    
    # Processing status
    is_processed = Column(Boolean, default=False)
    keywords_extracted = Column(Boolean, default=False)
    
    # Metadata
    original_source = Column(String(100))
    job_url = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="job_descriptions")
    keywords = relationship("Keyword", back_populates="job_description", cascade="all, delete-orphan")
    matching_scores = relationship("MatchingScore", back_populates="job_description", cascade="all, delete-orphan")
    
    def to_dict(self):
        """Convert job description to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'company_name': self.company_name,
            'job_description_text': self.job_description_text,
            'location': self.location,
            'employment_type': self.employment_type,
            'experience_level': self.experience_level,
            'salary_range': self.salary_range,
            'is_processed': self.is_processed,
            'keywords_extracted': self.keywords_extracted,
            'original_source': self.original_source,
            'job_url': self.job_url,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Keyword(db.Model):
    """
    Keyword Model - US-05 Keyword Parsing

    Stores extracted keywords from resumes and job descriptions
    """
    __tablename__ = 'keywords'

    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign keys
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    resume_id = Column(String(36), ForeignKey('resumes.id', ondelete='CASCADE'), nullable=True)
    job_description_id = Column(String(36), ForeignKey('job_descriptions.id', ondelete='CASCADE'), nullable=True)

    # Keyword information
    keyword = Column(String(100), nullable=False)
    keyword_type = Column(String(50), default='general')  # skill, technology, experience, education
    frequency = Column(Integer, default=1)
    confidence_score = Column(Float, default=0.80)

    # Context
    context_snippet = Column(Text)
    position_in_text = Column(Integer)

    # Processing metadata
    extraction_method = Column(String(50), default='nltk')  # nltk, spacy, manual
    is_verified = Column(Boolean, default=False)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="keywords")
    resume = relationship("Resume", back_populates="keywords")
    job_description = relationship("JobDescription", back_populates="keywords")

    def to_dict(self):
        """Convert keyword to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'resume_id': self.resume_id,
            'job_description_id': self.job_description_id,
            'keyword': self.keyword,
            'keyword_type': self.keyword_type,
            'frequency': self.frequency,
            'confidence_score': self.confidence_score,
            'context_snippet': self.context_snippet,
            'extraction_method': self.extraction_method,
            'is_verified': self.is_verified,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class MatchingScore(db.Model):
    """
    Matching Score Model - US-06 Matching Score

    Stores calculated matching scores between resumes and job descriptions
    """
    __tablename__ = 'matching_scores'

    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign keys
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    resume_id = Column(String(36), ForeignKey('resumes.id', ondelete='CASCADE'), nullable=False)
    job_description_id = Column(String(36), ForeignKey('job_descriptions.id', ondelete='CASCADE'), nullable=False)

    # Overall scores
    overall_match_percentage = Column(Float, default=0.0)
    jaccard_similarity = Column(Float, default=0.0)

    # Detailed breakdown
    skill_match_percentage = Column(Float, default=0.0)
    experience_match_percentage = Column(Float, default=0.0)
    education_match_percentage = Column(Float, default=0.0)

    # Keyword analysis
    keyword_overlap_count = Column(Integer, default=0)
    resume_keyword_count = Column(Integer, default=0)
    jd_keyword_count = Column(Integer, default=0)
    matched_keywords = Column(Text)  # JSON array
    missing_keywords = Column(Text)  # JSON array
    extra_keywords = Column(Text)   # JSON array

    # Calculation metadata
    calculation_method = Column(String(50), default='jaccard')
    confidence_score = Column(Float, default=0.0)
    is_current = Column(Boolean, default=True)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    last_calculated_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="matching_scores")
    resume = relationship("Resume", back_populates="matching_scores")
    job_description = relationship("JobDescription", back_populates="matching_scores")
    suggestions = relationship("Suggestion", back_populates="matching_score", cascade="all, delete-orphan")

    def to_dict(self):
        """Convert matching score to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'resume_id': self.resume_id,
            'job_description_id': self.job_description_id,
            'overall_match_percentage': self.overall_match_percentage,
            'jaccard_similarity': self.jaccard_similarity,
            'skill_match_percentage': self.skill_match_percentage,
            'experience_match_percentage': self.experience_match_percentage,
            'education_match_percentage': self.education_match_percentage,
            'keyword_overlap_count': self.keyword_overlap_count,
            'resume_keyword_count': self.resume_keyword_count,
            'jd_keyword_count': self.jd_keyword_count,
            'matched_keywords': json.loads(self.matched_keywords) if self.matched_keywords else [],
            'missing_keywords': json.loads(self.missing_keywords) if self.missing_keywords else [],
            'extra_keywords': json.loads(self.extra_keywords) if self.extra_keywords else [],
            'calculation_method': self.calculation_method,
            'confidence_score': self.confidence_score,
            'is_current': self.is_current,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_calculated_at': self.last_calculated_at.isoformat() if self.last_calculated_at else None
        }

class Suggestion(db.Model):
    """
    Suggestion Model - US-07 Suggestions

    Stores both basic and premium suggestions for resume improvement
    """
    __tablename__ = 'suggestions'

    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign keys
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    matching_score_id = Column(String(36), ForeignKey('matching_scores.id', ondelete='CASCADE'), nullable=False)

    # Suggestion content
    suggestion_type = Column(String(50), nullable=False)  # basic, premium
    category = Column(String(50), nullable=False)  # keyword, skill, experience, format
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)
    priority = Column(String(20), default='medium')  # low, medium, high, critical

    # Implementation details
    implementation_steps = Column(Text)  # JSON array of steps
    examples = Column(Text)  # JSON array of examples

    # AI-generated content (for premium suggestions)
    ai_generated = Column(Boolean, default=False)
    ai_model_used = Column(String(50))  # gpt-3.5-turbo, gpt-4, etc.
    ai_tokens_used = Column(Integer, default=0)
    ai_cost = Column(Float, default=0.0)

    # User interaction
    status = Column(String(20), default='pending')  # pending, implemented, dismissed, saved
    user_rating = Column(Integer)  # 1-5 stars
    user_feedback = Column(Text)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    implemented_at = Column(DateTime)

    # Relationships
    user = relationship("User", back_populates="suggestions")
    matching_score = relationship("MatchingScore", back_populates="suggestions")

    def to_dict(self):
        """Convert suggestion to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'matching_score_id': self.matching_score_id,
            'suggestion_type': self.suggestion_type,
            'category': self.category,
            'title': self.title,
            'description': self.description,
            'priority': self.priority,
            'implementation_steps': json.loads(self.implementation_steps) if self.implementation_steps else [],
            'examples': json.loads(self.examples) if self.examples else [],
            'ai_generated': self.ai_generated,
            'ai_model_used': self.ai_model_used,
            'status': self.status,
            'user_rating': self.user_rating,
            'user_feedback': self.user_feedback,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'implemented_at': self.implemented_at.isoformat() if self.implemented_at else None
        }

class ScanHistory(db.Model):
    """
    Scan History Model - US-08 Dashboard

    Comprehensive tracking of all resume scanning activities
    """
    __tablename__ = 'scan_history'

    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign keys
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    resume_id = Column(String(36), ForeignKey('resumes.id', ondelete='CASCADE'), nullable=False)
    job_description_id = Column(String(36), ForeignKey('job_descriptions.id', ondelete='CASCADE'), nullable=False)
    matching_score_id = Column(String(36), ForeignKey('matching_scores.id', ondelete='CASCADE'), nullable=True)

    # Scan metadata
    scan_name = Column(String(200), nullable=False)
    scan_description = Column(Text)
    scan_status = Column(String(20), default='pending')  # pending, processing, completed, failed

    # Resume information (denormalized for performance)
    resume_filename = Column(String(255))
    resume_file_size = Column(Integer)

    # Job information (denormalized for performance)
    job_title = Column(String(200))
    company_name = Column(String(200))
    job_location = Column(String(200))

    # Matching results
    overall_match_score = Column(Float, default=0.0)
    keyword_match_score = Column(Float, default=0.0)
    skill_match_score = Column(Float, default=0.0)
    experience_match_score = Column(Float, default=0.0)

    # Keyword analysis
    total_keywords_found = Column(Integer, default=0)
    missing_keywords_count = Column(Integer, default=0)
    matched_keywords = Column(Text)  # JSON array
    missing_keywords = Column(Text)  # JSON array

    # Suggestions summary
    total_suggestions = Column(Integer, default=0)
    high_priority_suggestions = Column(Integer, default=0)
    implemented_suggestions = Column(Integer, default=0)

    # Premium features
    has_premium_suggestions = Column(Boolean, default=False)
    premium_cost = Column(Float, default=0.0)
    ai_tokens_used = Column(Integer, default=0)

    # Processing metrics
    processing_time_seconds = Column(Float, default=0.0)
    processing_start_time = Column(DateTime)
    processing_end_time = Column(DateTime)

    # User interaction
    last_viewed_at = Column(DateTime)
    view_count = Column(Integer, default=0)
    is_bookmarked = Column(Boolean, default=False)
    user_rating = Column(Integer)  # 1-5 stars
    user_notes = Column(Text)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="scan_history")

    def to_dict(self):
        """Convert scan history to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'resume_id': self.resume_id,
            'job_description_id': self.job_description_id,
            'matching_score_id': self.matching_score_id,
            'scan_name': self.scan_name,
            'scan_description': self.scan_description,
            'scan_status': self.scan_status,
            'resume_filename': self.resume_filename,
            'job_title': self.job_title,
            'company_name': self.company_name,
            'job_location': self.job_location,
            'overall_match_score': self.overall_match_score,
            'keyword_match_score': self.keyword_match_score,
            'skill_match_score': self.skill_match_score,
            'experience_match_score': self.experience_match_score,
            'total_keywords_found': self.total_keywords_found,
            'missing_keywords_count': self.missing_keywords_count,
            'matched_keywords': json.loads(self.matched_keywords) if self.matched_keywords else [],
            'missing_keywords': json.loads(self.missing_keywords) if self.missing_keywords else [],
            'total_suggestions': self.total_suggestions,
            'high_priority_suggestions': self.high_priority_suggestions,
            'implemented_suggestions': self.implemented_suggestions,
            'has_premium_suggestions': self.has_premium_suggestions,
            'premium_cost': self.premium_cost,
            'processing_time_seconds': self.processing_time_seconds,
            'is_bookmarked': self.is_bookmarked,
            'user_rating': self.user_rating,
            'user_notes': self.user_notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class UserProfile(db.Model):
    """
    User Profile Model - US-10 Account Settings

    Extended user profile information beyond basic authentication
    """
    __tablename__ = 'user_profiles'

    # Primary key
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # Foreign key
    user_id = Column(String(36), ForeignKey('users.id', ondelete='CASCADE'), nullable=False, unique=True)

    # Personal information
    display_name = Column(String(100))
    bio = Column(Text)
    phone_number = Column(String(20))
    country = Column(String(2))  # ISO country code
    timezone = Column(String(50))

    # Professional information
    current_position = Column(String(100))
    current_company = Column(String(100))
    years_of_experience = Column(Integer)
    industry = Column(String(100))
    skills = Column(Text)  # JSON array

    # Preferences
    email_notifications = Column(Boolean, default=True)
    marketing_emails = Column(Boolean, default=False)
    data_sharing = Column(Boolean, default=False)
    preferred_language = Column(String(10), default='en')

    # Subscription information
    subscription_status = Column(String(20), default='free')  # free, premium, enterprise
    subscription_start_date = Column(DateTime)
    subscription_end_date = Column(DateTime)
    billing_cycle = Column(String(20))  # monthly, yearly

    # Usage statistics
    total_scans = Column(Integer, default=0)
    premium_scans_used = Column(Integer, default=0)
    premium_scans_limit = Column(Integer, default=0)
    api_calls_this_month = Column(Integer, default=0)

    # Security settings
    two_factor_enabled = Column(Boolean, default=False)
    login_notifications = Column(Boolean, default=True)
    session_timeout_minutes = Column(Integer, default=60)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_profile_update = Column(DateTime)

    # Relationships
    user = relationship("User", back_populates="user_profile")

    def to_dict(self):
        """Convert user profile to dictionary for JSON responses"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'display_name': self.display_name,
            'bio': self.bio,
            'phone_number': self.phone_number,
            'country': self.country,
            'timezone': self.timezone,
            'current_position': self.current_position,
            'current_company': self.current_company,
            'years_of_experience': self.years_of_experience,
            'industry': self.industry,
            'skills': json.loads(self.skills) if self.skills else [],
            'email_notifications': self.email_notifications,
            'marketing_emails': self.marketing_emails,
            'data_sharing': self.data_sharing,
            'preferred_language': self.preferred_language,
            'subscription_status': self.subscription_status,
            'subscription_start_date': self.subscription_start_date.isoformat() if self.subscription_start_date else None,
            'subscription_end_date': self.subscription_end_date.isoformat() if self.subscription_end_date else None,
            'billing_cycle': self.billing_cycle,
            'total_scans': self.total_scans,
            'premium_scans_used': self.premium_scans_used,
            'premium_scans_limit': self.premium_scans_limit,
            'two_factor_enabled': self.two_factor_enabled,
            'login_notifications': self.login_notifications,
            'session_timeout_minutes': self.session_timeout_minutes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_profile_update': self.last_profile_update.isoformat() if self.last_profile_update else None
        }
