"""
Matching Engine Service - US-06
===============================

This service calculates matching scores between resumes and job descriptions.
Implements US-06 (Matching Score) functionality using various algorithms.

Tech Stack: scikit-learn, NLTK, custom algorithms
"""

import re
import logging
from typing import Dict, List, Set, Optional
from collections import Counter

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("Warning: scikit-learn not available. Using basic matching algorithms.")

from models import Keyword

class MatchingEngine:
    """
    Engine for calculating matching scores between resumes and job descriptions
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_match(self, resume_text: str, job_description_text: str, 
                       resume_id: str, job_description_id: str, 
                       method: str = 'jaccard') -> Optional[Dict]:
        """
        Calculate matching score between resume and job description
        
        Args:
            resume_text (str): Extracted resume text
            job_description_text (str): Job description text
            resume_id (str): Resume ID for keyword lookup
            job_description_id (str): Job description ID for keyword lookup
            method (str): Calculation method ('jaccard', 'weighted', 'hybrid')
            
        Returns:
            Optional[Dict]: Matching results or None if calculation failed
        """
        try:
            # Get keywords from database
            resume_keywords = self._get_keywords_from_db(resume_id, 'resume')
            jd_keywords = self._get_keywords_from_db(job_description_id, 'job_description')
            
            # If no keywords in DB, extract them from text
            if not resume_keywords:
                resume_keywords = self._extract_keywords_from_text(resume_text)
            
            if not jd_keywords:
                jd_keywords = self._extract_keywords_from_text(job_description_text)
            
            # Calculate match based on method
            if method == 'jaccard':
                return self._calculate_jaccard_similarity(resume_keywords, jd_keywords)
            elif method == 'weighted':
                return self._calculate_weighted_similarity(resume_keywords, jd_keywords, resume_text, job_description_text)
            elif method == 'hybrid':
                return self._calculate_hybrid_similarity(resume_keywords, jd_keywords, resume_text, job_description_text)
            else:
                self.logger.error(f"Unknown matching method: {method}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error calculating match: {str(e)}")
            return None
    
    def _get_keywords_from_db(self, document_id: str, document_type: str) -> List[Dict]:
        """Get keywords from database"""
        try:
            if document_type == 'resume':
                keywords = Keyword.query.filter_by(resume_id=document_id).all()
            else:
                keywords = Keyword.query.filter_by(job_description_id=document_id).all()
            
            return [
                {
                    'keyword': kw.keyword.lower(),
                    'type': kw.keyword_type,
                    'frequency': kw.frequency,
                    'confidence': kw.confidence_score
                }
                for kw in keywords
            ]
            
        except Exception as e:
            self.logger.warning(f"Could not get keywords from DB: {e}")
            return []
    
    def _extract_keywords_from_text(self, text: str) -> List[Dict]:
        """Extract keywords from text as fallback"""
        try:
            # Simple keyword extraction
            words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
            word_freq = Counter(words)
            
            # Filter common words
            stopwords = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use'}
            
            keywords = []
            for word, freq in word_freq.most_common(50):
                if word not in stopwords and len(word) > 3:
                    keywords.append({
                        'keyword': word,
                        'type': 'general',
                        'frequency': freq,
                        'confidence': 0.7
                    })
            
            return keywords
            
        except Exception as e:
            self.logger.error(f"Error extracting keywords from text: {e}")
            return []
    
    def _calculate_jaccard_similarity(self, resume_keywords: List[Dict], jd_keywords: List[Dict]) -> Dict:
        """Calculate Jaccard similarity between keyword sets"""
        try:
            # Convert to sets of keywords
            resume_set = set(kw['keyword'] for kw in resume_keywords)
            jd_set = set(kw['keyword'] for kw in jd_keywords)
            
            # Calculate Jaccard similarity
            intersection = resume_set.intersection(jd_set)
            union = resume_set.union(jd_set)
            
            jaccard_similarity = len(intersection) / len(union) if union else 0.0
            overall_match_percentage = jaccard_similarity * 100
            
            # Calculate keyword-specific matches
            matched_keywords = list(intersection)
            missing_keywords = list(jd_set - resume_set)
            extra_keywords = list(resume_set - jd_set)
            
            # Calculate category-specific matches
            skill_match = self._calculate_category_match(resume_keywords, jd_keywords, 'skill')
            tech_match = self._calculate_category_match(resume_keywords, jd_keywords, 'technology')
            experience_match = self._calculate_category_match(resume_keywords, jd_keywords, 'experience')
            education_match = self._calculate_category_match(resume_keywords, jd_keywords, 'education')
            
            return {
                'overall_match_percentage': round(overall_match_percentage, 2),
                'jaccard_similarity': round(jaccard_similarity, 4),
                'skill_match_percentage': round(skill_match, 2),
                'experience_match_percentage': round(experience_match, 2),
                'education_match_percentage': round(education_match, 2),
                'keyword_overlap_count': len(intersection),
                'resume_keyword_count': len(resume_set),
                'jd_keyword_count': len(jd_set),
                'matched_keywords': matched_keywords,
                'missing_keywords': missing_keywords,
                'extra_keywords': extra_keywords,
                'confidence_score': 0.8
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating Jaccard similarity: {e}")
            return {}
    
    def _calculate_weighted_similarity(self, resume_keywords: List[Dict], jd_keywords: List[Dict], 
                                     resume_text: str, job_description_text: str) -> Dict:
        """Calculate weighted similarity considering keyword importance"""
        try:
            # Create weighted keyword dictionaries
            resume_weighted = {}
            for kw in resume_keywords:
                weight = self._calculate_keyword_weight(kw)
                resume_weighted[kw['keyword']] = weight
            
            jd_weighted = {}
            for kw in jd_keywords:
                weight = self._calculate_keyword_weight(kw)
                jd_weighted[kw['keyword']] = weight
            
            # Calculate weighted intersection
            all_keywords = set(resume_weighted.keys()).union(set(jd_weighted.keys()))
            
            weighted_intersection = 0.0
            weighted_union = 0.0
            matched_keywords = []
            missing_keywords = []
            
            for keyword in all_keywords:
                resume_weight = resume_weighted.get(keyword, 0.0)
                jd_weight = jd_weighted.get(keyword, 0.0)
                
                if resume_weight > 0 and jd_weight > 0:
                    # Keyword present in both
                    weighted_intersection += min(resume_weight, jd_weight)
                    matched_keywords.append(keyword)
                elif jd_weight > 0:
                    # Keyword missing from resume
                    missing_keywords.append(keyword)
                
                weighted_union += max(resume_weight, jd_weight)
            
            # Calculate similarity
            weighted_similarity = weighted_intersection / weighted_union if weighted_union > 0 else 0.0
            overall_match_percentage = weighted_similarity * 100
            
            # Use TF-IDF if available
            if SKLEARN_AVAILABLE:
                tfidf_similarity = self._calculate_tfidf_similarity(resume_text, job_description_text)
                overall_match_percentage = (overall_match_percentage + tfidf_similarity * 100) / 2
            
            # Calculate category-specific matches
            skill_match = self._calculate_category_match(resume_keywords, jd_keywords, 'skill')
            tech_match = self._calculate_category_match(resume_keywords, jd_keywords, 'technology')
            experience_match = self._calculate_category_match(resume_keywords, jd_keywords, 'experience')
            education_match = self._calculate_category_match(resume_keywords, jd_keywords, 'education')
            
            return {
                'overall_match_percentage': round(overall_match_percentage, 2),
                'jaccard_similarity': round(weighted_similarity, 4),
                'skill_match_percentage': round(skill_match, 2),
                'experience_match_percentage': round(experience_match, 2),
                'education_match_percentage': round(education_match, 2),
                'keyword_overlap_count': len(matched_keywords),
                'resume_keyword_count': len(resume_weighted),
                'jd_keyword_count': len(jd_weighted),
                'matched_keywords': matched_keywords,
                'missing_keywords': missing_keywords,
                'extra_keywords': list(set(resume_weighted.keys()) - set(jd_weighted.keys())),
                'confidence_score': 0.85
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating weighted similarity: {e}")
            return self._calculate_jaccard_similarity(resume_keywords, jd_keywords)
    
    def _calculate_hybrid_similarity(self, resume_keywords: List[Dict], jd_keywords: List[Dict], 
                                   resume_text: str, job_description_text: str) -> Dict:
        """Calculate hybrid similarity combining multiple methods"""
        try:
            # Get Jaccard similarity
            jaccard_result = self._calculate_jaccard_similarity(resume_keywords, jd_keywords)
            
            # Get weighted similarity
            weighted_result = self._calculate_weighted_similarity(resume_keywords, jd_keywords, resume_text, job_description_text)
            
            # Combine results
            jaccard_score = jaccard_result.get('overall_match_percentage', 0.0)
            weighted_score = weighted_result.get('overall_match_percentage', 0.0)
            
            # Weighted average (favor weighted similarity)
            hybrid_score = (jaccard_score * 0.3) + (weighted_score * 0.7)
            
            # Use the more detailed result as base
            result = weighted_result.copy()
            result['overall_match_percentage'] = round(hybrid_score, 2)
            result['confidence_score'] = 0.9
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error calculating hybrid similarity: {e}")
            return self._calculate_jaccard_similarity(resume_keywords, jd_keywords)
    
    def _calculate_keyword_weight(self, keyword_data: Dict) -> float:
        """Calculate weight for a keyword based on type, frequency, and confidence"""
        base_weight = keyword_data.get('frequency', 1)
        confidence = keyword_data.get('confidence', 0.7)
        keyword_type = keyword_data.get('type', 'general')
        
        # Type-based multipliers
        type_multipliers = {
            'technology': 1.5,
            'skill': 1.3,
            'experience': 1.2,
            'education': 1.1,
            'general': 1.0
        }
        
        type_multiplier = type_multipliers.get(keyword_type, 1.0)
        
        return base_weight * confidence * type_multiplier
    
    def _calculate_category_match(self, resume_keywords: List[Dict], jd_keywords: List[Dict], category: str) -> float:
        """Calculate match percentage for a specific keyword category"""
        try:
            resume_category = set(kw['keyword'] for kw in resume_keywords if kw.get('type') == category)
            jd_category = set(kw['keyword'] for kw in jd_keywords if kw.get('type') == category)
            
            if not jd_category:
                return 100.0  # No requirements in this category
            
            intersection = resume_category.intersection(jd_category)
            return (len(intersection) / len(jd_category)) * 100 if jd_category else 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating category match for {category}: {e}")
            return 0.0
    
    def _calculate_tfidf_similarity(self, text1: str, text2: str) -> float:
        """Calculate TF-IDF cosine similarity between two texts"""
        try:
            if not SKLEARN_AVAILABLE:
                return 0.0
            
            # Create TF-IDF vectors
            vectorizer = TfidfVectorizer(
                stop_words='english',
                max_features=1000,
                ngram_range=(1, 2)
            )
            
            tfidf_matrix = vectorizer.fit_transform([text1, text2])
            
            # Calculate cosine similarity
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
            
            return float(similarity)
            
        except Exception as e:
            self.logger.error(f"Error calculating TF-IDF similarity: {e}")
            return 0.0
