# Dr. Resume - Environment Configuration Template
# ==============================================
# 
# Copy this file to .env and update the values with your actual configuration.
# This file contains all the environment variables needed for the application.

# Application Configuration
# -------------------------
# Secret key for Flask sessions and security (CHANGE THIS IN PRODUCTION!)
SECRET_KEY=your-super-secret-key-change-this-in-production

# JWT secret key for token signing (CHANGE THIS IN PRODUCTION!)
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production

# Application environment (development, production, testing)
FLASK_ENV=development

# Debug mode (True for development, False for production)
FLASK_DEBUG=True

# Database Configuration
# ----------------------
# SQLite database URL (default for development)
DATABASE_URL=sqlite:///dr_resume.db

# PostgreSQL database URL (for production)
# DATABASE_URL=postgresql://username:password@localhost:5432/dr_resume

# MySQL database URL (alternative)
# DATABASE_URL=mysql://username:password@localhost:3306/dr_resume

# File Upload Configuration
# -------------------------
# Directory for uploaded files (absolute path recommended for production)
UPLOAD_FOLDER=uploads

# Maximum file size for uploads (in bytes, default: 10MB)
MAX_CONTENT_LENGTH=10485760

# OpenAI API Configuration (for Premium Suggestions)
# --------------------------------------------------
# OpenAI API key for premium AI suggestions (US-07)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your-openai-api-key-here

# OpenAI model to use (gpt-3.5-turbo, gpt-4, etc.)
OPENAI_MODEL=gpt-3.5-turbo

# Maximum tokens for OpenAI requests
OPENAI_MAX_TOKENS=1500

# Email Configuration (for notifications - optional)
# --------------------------------------------------
# SMTP server configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USE_SSL=False

# Email credentials
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Default sender email
MAIL_DEFAULT_SENDER=<EMAIL>

# Redis Configuration (for caching - optional)
# --------------------------------------------
# Redis URL for caching and session storage
REDIS_URL=redis://localhost:6379/0

# Security Configuration
# ----------------------
# CORS allowed origins (comma-separated)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:5000

# Rate limiting (requests per minute)
RATE_LIMIT_PER_MINUTE=100

# Session timeout (in minutes)
SESSION_TIMEOUT=60

# Logging Configuration
# --------------------
# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log file path (optional, logs to console if not set)
LOG_FILE=logs/dr_resume.log

# Sentry DSN for error tracking (optional)
SENTRY_DSN=your-sentry-dsn-here

# Third-party API Keys (optional)
# -------------------------------
# Google Analytics tracking ID
GOOGLE_ANALYTICS_ID=your-ga-tracking-id

# Stripe API keys for payment processing (if implementing premium subscriptions)
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# AWS S3 Configuration (for file storage - optional)
# --------------------------------------------------
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket-name
AWS_S3_REGION=us-east-1

# Application URLs
# ---------------
# Frontend URL (for CORS and redirects)
FRONTEND_URL=http://localhost:5000

# API base URL
API_BASE_URL=http://localhost:5000/api

# Feature Flags
# -------------
# Enable/disable premium features
ENABLE_PREMIUM_FEATURES=True

# Enable/disable user registration
ENABLE_USER_REGISTRATION=True

# Enable/disable email notifications
ENABLE_EMAIL_NOTIFICATIONS=False

# Enable/disable analytics tracking
ENABLE_ANALYTICS=False

# Development Configuration
# -------------------------
# Enable SQL query logging in development
SQLALCHEMY_ECHO=False

# Enable Flask toolbar for debugging
FLASK_TOOLBAR_ENABLED=False

# Production Configuration
# ------------------------
# Number of worker processes for production server
WORKERS=4

# Worker timeout (in seconds)
WORKER_TIMEOUT=30

# Maximum number of requests per worker
MAX_REQUESTS=1000

# SSL Configuration (for HTTPS)
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# Backup Configuration
# -------------------
# Database backup directory
BACKUP_DIR=backups

# Backup retention days
BACKUP_RETENTION_DAYS=30

# Monitoring Configuration
# -----------------------
# Health check endpoint token
HEALTH_CHECK_TOKEN=your-health-check-token

# Metrics collection endpoint
METRICS_ENDPOINT=/metrics

# Admin Configuration
# ------------------
# Admin user email (will be created with admin privileges)
ADMIN_EMAIL=<EMAIL>

# Admin user password (CHANGE THIS!)
ADMIN_PASSWORD=admin-password-change-this
