"""
Dashboard Routes - US-08
========================

This module handles dashboard and history functionality.
Implements US-08 (Dashboard + History) functionality.

Routes:
- GET /api/history - Get user's scan history
- GET /api/analytics - Get user analytics
- POST /api/history - Create scan history entry
- PUT /api/history/<id> - Update scan history entry
- DELETE /api/history/<id> - Delete scan history entry
"""

import json
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import func, desc
from models import db, ScanHistory, Resume, JobDescription, MatchingScore, Suggestion, User

# Create Blueprint
dashboard_bp = Blueprint('dashboard', __name__, url_prefix='/api')

@dashboard_bp.route('/history', methods=['GET'])
@jwt_required()
def get_scan_history():
    """
    Get User's Scan History Endpoint - US-08
    
    Returns paginated scan history for the current user
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        status_filter = request.args.get('status')  # pending, processing, completed, failed
        date_from = request.args.get('date_from')  # YYYY-MM-DD
        date_to = request.args.get('date_to')  # YYYY-MM-DD
        
        # Build query
        query = ScanHistory.query.filter_by(user_id=current_user_id)
        
        # Apply filters
        if status_filter:
            query = query.filter(ScanHistory.scan_status == status_filter)
        
        if date_from:
            try:
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
                query = query.filter(ScanHistory.created_at >= date_from_obj)
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': 'Invalid date_from format. Use YYYY-MM-DD.'
                }), 400
        
        if date_to:
            try:
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
                query = query.filter(ScanHistory.created_at < date_to_obj)
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': 'Invalid date_to format. Use YYYY-MM-DD.'
                }), 400
        
        # Order by creation date (newest first)
        query = query.order_by(desc(ScanHistory.created_at))
        
        # Paginate results
        history_paginated = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        return jsonify({
            'success': True,
            'scan_history': [scan.to_dict() for scan in history_paginated.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': history_paginated.total,
                'pages': history_paginated.pages,
                'has_next': history_paginated.has_next,
                'has_prev': history_paginated.has_prev
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get scan history: {str(e)}'
        }), 500

@dashboard_bp.route('/analytics', methods=['GET'])
@jwt_required()
def get_user_analytics():
    """
    Get User Analytics Endpoint - US-08
    
    Returns comprehensive analytics for the current user
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Get time period parameter
        period = request.args.get('period', '30')  # days
        try:
            days = int(period)
        except ValueError:
            days = 30
        
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Basic counts
        total_resumes = Resume.query.filter_by(user_id=current_user_id, is_active=True).count()
        total_job_descriptions = JobDescription.query.filter_by(user_id=current_user_id).count()
        total_scans = ScanHistory.query.filter_by(user_id=current_user_id).count()
        
        # Recent activity (within the period)
        recent_scans = ScanHistory.query.filter(
            ScanHistory.user_id == current_user_id,
            ScanHistory.created_at >= start_date
        ).count()
        
        recent_resumes = Resume.query.filter(
            Resume.user_id == current_user_id,
            Resume.created_at >= start_date,
            Resume.is_active == True
        ).count()
        
        recent_job_descriptions = JobDescription.query.filter(
            JobDescription.user_id == current_user_id,
            JobDescription.created_at >= start_date
        ).count()
        
        # Matching score statistics
        matching_scores = MatchingScore.query.filter(
            MatchingScore.user_id == current_user_id,
            MatchingScore.is_current == True
        ).all()
        
        if matching_scores:
            scores = [ms.overall_match_percentage for ms in matching_scores]
            avg_match_score = sum(scores) / len(scores)
            highest_match_score = max(scores)
            lowest_match_score = min(scores)
        else:
            avg_match_score = 0.0
            highest_match_score = 0.0
            lowest_match_score = 0.0
        
        # Suggestion statistics
        total_suggestions = Suggestion.query.filter_by(user_id=current_user_id).count()
        implemented_suggestions = Suggestion.query.filter_by(
            user_id=current_user_id,
            status='implemented'
        ).count()
        
        premium_suggestions = Suggestion.query.filter_by(
            user_id=current_user_id,
            suggestion_type='premium'
        ).count()
        
        # Scan status distribution
        scan_status_counts = db.session.query(
            ScanHistory.scan_status,
            func.count(ScanHistory.id)
        ).filter_by(user_id=current_user_id).group_by(ScanHistory.scan_status).all()
        
        scan_status_distribution = {status: count for status, count in scan_status_counts}
        
        # Recent activity timeline (last 7 days)
        timeline = []
        for i in range(7):
            date = end_date - timedelta(days=i)
            day_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
            day_end = day_start + timedelta(days=1)
            
            day_scans = ScanHistory.query.filter(
                ScanHistory.user_id == current_user_id,
                ScanHistory.created_at >= day_start,
                ScanHistory.created_at < day_end
            ).count()
            
            timeline.append({
                'date': day_start.strftime('%Y-%m-%d'),
                'scans': day_scans
            })
        
        timeline.reverse()  # Oldest first
        
        # Top performing resumes (by average match score)
        top_resumes = db.session.query(
            Resume.id,
            Resume.resume_title,
            Resume.original_filename,
            func.avg(MatchingScore.overall_match_percentage).label('avg_score'),
            func.count(MatchingScore.id).label('scan_count')
        ).join(MatchingScore).filter(
            Resume.user_id == current_user_id,
            Resume.is_active == True,
            MatchingScore.is_current == True
        ).group_by(Resume.id).order_by(desc('avg_score')).limit(5).all()
        
        top_resumes_data = [
            {
                'resume_id': resume.id,
                'title': resume.resume_title,
                'filename': resume.original_filename,
                'average_score': round(resume.avg_score, 2),
                'scan_count': resume.scan_count
            }
            for resume in top_resumes
        ]
        
        # User profile info
        user = User.query.get(current_user_id)
        
        return jsonify({
            'success': True,
            'analytics': {
                'period_days': days,
                'overview': {
                    'total_resumes': total_resumes,
                    'total_job_descriptions': total_job_descriptions,
                    'total_scans': total_scans,
                    'total_suggestions': total_suggestions,
                    'implemented_suggestions': implemented_suggestions,
                    'premium_suggestions': premium_suggestions
                },
                'recent_activity': {
                    'recent_scans': recent_scans,
                    'recent_resumes': recent_resumes,
                    'recent_job_descriptions': recent_job_descriptions
                },
                'matching_scores': {
                    'average_match_score': round(avg_match_score, 2),
                    'highest_match_score': round(highest_match_score, 2),
                    'lowest_match_score': round(lowest_match_score, 2),
                    'total_matches': len(matching_scores)
                },
                'scan_status_distribution': scan_status_distribution,
                'activity_timeline': timeline,
                'top_performing_resumes': top_resumes_data,
                'user_info': {
                    'is_premium': user.is_premium if user else False,
                    'member_since': user.created_at.strftime('%Y-%m-%d') if user and user.created_at else None
                }
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get analytics: {str(e)}'
        }), 500

@dashboard_bp.route('/history', methods=['POST'])
@jwt_required()
def create_scan_history():
    """
    Create Scan History Entry Endpoint - US-08
    
    Creates a new scan history entry (usually called after matching calculation)
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Extract required fields
        resume_id = data.get('resume_id', '').strip()
        job_description_id = data.get('job_description_id', '').strip()
        scan_name = data.get('scan_name', '').strip()
        
        # Validate required fields
        if not resume_id or not job_description_id or not scan_name:
            return jsonify({
                'success': False,
                'message': 'Resume ID, Job Description ID, and scan name are required'
            }), 400
        
        # Verify resume and job description belong to user
        resume = Resume.query.filter_by(id=resume_id, user_id=current_user_id).first()
        job_description = JobDescription.query.filter_by(id=job_description_id, user_id=current_user_id).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        if not job_description:
            return jsonify({
                'success': False,
                'message': 'Job description not found'
            }), 404
        
        # Get matching score if available
        matching_score = MatchingScore.query.filter_by(
            user_id=current_user_id,
            resume_id=resume_id,
            job_description_id=job_description_id,
            is_current=True
        ).first()
        
        # Create scan history entry
        scan_history = ScanHistory(
            user_id=current_user_id,
            resume_id=resume_id,
            job_description_id=job_description_id,
            matching_score_id=matching_score.id if matching_score else None,
            scan_name=scan_name,
            scan_description=data.get('scan_description', ''),
            scan_status='completed',
            resume_filename=resume.original_filename,
            resume_file_size=resume.file_size,
            job_title=job_description.title,
            company_name=job_description.company_name,
            job_location=job_description.location,
            overall_match_score=matching_score.overall_match_percentage if matching_score else 0.0,
            keyword_match_score=matching_score.jaccard_similarity * 100 if matching_score else 0.0,
            skill_match_score=matching_score.skill_match_percentage if matching_score else 0.0,
            experience_match_score=matching_score.experience_match_percentage if matching_score else 0.0,
            total_keywords_found=matching_score.keyword_overlap_count if matching_score else 0,
            missing_keywords_count=len(json.loads(matching_score.missing_keywords)) if matching_score and matching_score.missing_keywords else 0,
            matched_keywords=matching_score.matched_keywords if matching_score else '[]',
            missing_keywords=matching_score.missing_keywords if matching_score else '[]'
        )
        
        db.session.add(scan_history)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Scan history entry created successfully',
            'scan_history': scan_history.to_dict()
        }), 201
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to create scan history: {str(e)}'
        }), 500

@dashboard_bp.route('/history/<history_id>', methods=['PUT'])
@jwt_required()
def update_scan_history(history_id):
    """
    Update Scan History Entry Endpoint - US-08
    
    Updates scan history metadata (bookmarks, notes, ratings)
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find scan history entry
        scan_history = ScanHistory.query.filter_by(
            id=history_id,
            user_id=current_user_id
        ).first()
        
        if not scan_history:
            return jsonify({
                'success': False,
                'message': 'Scan history entry not found'
            }), 404
        
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Update fields if provided
        if 'is_bookmarked' in data:
            scan_history.is_bookmarked = bool(data['is_bookmarked'])
        
        if 'user_rating' in data:
            rating = data['user_rating']
            if rating is not None and (not isinstance(rating, int) or rating < 1 or rating > 5):
                return jsonify({
                    'success': False,
                    'message': 'User rating must be an integer between 1 and 5'
                }), 400
            scan_history.user_rating = rating
        
        if 'user_notes' in data:
            scan_history.user_notes = data['user_notes']
        
        if 'scan_name' in data:
            scan_name = data['scan_name'].strip()
            if scan_name:
                scan_history.scan_name = scan_name
        
        if 'scan_description' in data:
            scan_history.scan_description = data['scan_description']
        
        # Update view tracking
        scan_history.last_viewed_at = datetime.utcnow()
        scan_history.view_count = (scan_history.view_count or 0) + 1
        scan_history.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Scan history updated successfully',
            'scan_history': scan_history.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to update scan history: {str(e)}'
        }), 500

@dashboard_bp.route('/history/<history_id>', methods=['DELETE'])
@jwt_required()
def delete_scan_history(history_id):
    """
    Delete Scan History Entry Endpoint - US-08
    
    Deletes a scan history entry
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find scan history entry
        scan_history = ScanHistory.query.filter_by(
            id=history_id,
            user_id=current_user_id
        ).first()
        
        if not scan_history:
            return jsonify({
                'success': False,
                'message': 'Scan history entry not found'
            }), 404
        
        # Delete the entry
        db.session.delete(scan_history)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Scan history entry deleted successfully'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to delete scan history: {str(e)}'
        }), 500
