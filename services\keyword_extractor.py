"""
Keyword Extractor Service - US-05
=================================

This service handles keyword extraction from text using NLP techniques.
Implements US-05 (Keyword Parsing) functionality.

Tech Stack: NLTK, spaCy (optional), scikit-learn
"""

import re
import logging
from typing import List, Dict, Set
from collections import Counter

try:
    import nltk
    from nltk.tokenize import word_tokenize, sent_tokenize
    from nltk.corpus import stopwords
    from nltk.tag import pos_tag
    from nltk.chunk import ne_chunk
    from nltk.stem import WordNetLemmatizer
    NLTK_AVAILABLE = True
    
    # Download required NLTK data (run once)
    try:
        nltk.data.find('tokenizers/punkt')
    except LookupError:
        nltk.download('punkt', quiet=True)
    
    try:
        nltk.data.find('corpora/stopwords')
    except LookupError:
        nltk.download('stopwords', quiet=True)
    
    try:
        nltk.data.find('taggers/averaged_perceptron_tagger')
    except LookupError:
        nltk.download('averaged_perceptron_tagger', quiet=True)
    
    try:
        nltk.data.find('corpora/wordnet')
    except LookupError:
        nltk.download('wordnet', quiet=True)
    
    try:
        nltk.data.find('chunkers/maxent_ne_chunker')
    except LookupError:
        nltk.download('maxent_ne_chunker', quiet=True)
    
    try:
        nltk.data.find('corpora/words')
    except LookupError:
        nltk.download('words', quiet=True)
        
except ImportError:
    NLTK_AVAILABLE = False
    print("Warning: NLTK not installed. Keyword extraction will use basic methods.")

class KeywordExtractor:
    """
    Keyword extractor using NLP techniques
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize NLTK components if available
        if NLTK_AVAILABLE:
            try:
                self.stop_words = set(stopwords.words('english'))
                self.lemmatizer = WordNetLemmatizer()
            except Exception as e:
                self.logger.warning(f"NLTK initialization warning: {e}")
                self.stop_words = set()
                self.lemmatizer = None
        else:
            self.stop_words = self._get_basic_stopwords()
            self.lemmatizer = None
        
        # Technical skills and keywords database
        self.tech_skills = self._load_tech_skills()
        self.soft_skills = self._load_soft_skills()
        self.education_keywords = self._load_education_keywords()
        self.experience_keywords = self._load_experience_keywords()
    
    def extract_keywords(self, text: str, source_type: str = 'general') -> List[Dict]:
        """
        Extract keywords from text
        
        Args:
            text (str): Input text
            source_type (str): Type of source ('resume', 'job_description', 'general')
            
        Returns:
            List[Dict]: List of extracted keywords with metadata
        """
        if not text or not text.strip():
            return []
        
        try:
            # Clean and preprocess text
            cleaned_text = self._preprocess_text(text)
            
            # Extract different types of keywords
            keywords = []
            
            # Extract technical skills
            tech_keywords = self._extract_technical_skills(cleaned_text)
            keywords.extend(tech_keywords)
            
            # Extract soft skills
            soft_keywords = self._extract_soft_skills(cleaned_text)
            keywords.extend(soft_keywords)
            
            # Extract education keywords
            education_keywords = self._extract_education_keywords(cleaned_text)
            keywords.extend(education_keywords)
            
            # Extract experience keywords
            experience_keywords = self._extract_experience_keywords(cleaned_text)
            keywords.extend(experience_keywords)
            
            # Extract general keywords using NLP
            if NLTK_AVAILABLE:
                nlp_keywords = self._extract_nlp_keywords(cleaned_text)
                keywords.extend(nlp_keywords)
            else:
                basic_keywords = self._extract_basic_keywords(cleaned_text)
                keywords.extend(basic_keywords)
            
            # Remove duplicates and rank by importance
            keywords = self._deduplicate_and_rank(keywords, text)
            
            return keywords
            
        except Exception as e:
            self.logger.error(f"Error extracting keywords: {str(e)}")
            return []
    
    def _preprocess_text(self, text: str) -> str:
        """Clean and preprocess text for keyword extraction"""
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters but keep spaces and periods
        text = re.sub(r'[^\w\s\.]', ' ', text)
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    def _extract_technical_skills(self, text: str) -> List[Dict]:
        """Extract technical skills and technologies"""
        keywords = []
        
        for skill in self.tech_skills:
            # Case-insensitive search with word boundaries
            pattern = r'\b' + re.escape(skill.lower()) + r'\b'
            matches = re.findall(pattern, text)
            
            if matches:
                # Find context for the first occurrence
                context = self._get_context(text, skill.lower())
                
                keywords.append({
                    'keyword': skill,
                    'type': 'technology',
                    'frequency': len(matches),
                    'confidence': 0.9,
                    'context': context
                })
        
        return keywords
    
    def _extract_soft_skills(self, text: str) -> List[Dict]:
        """Extract soft skills and competencies"""
        keywords = []
        
        for skill in self.soft_skills:
            pattern = r'\b' + re.escape(skill.lower()) + r'\b'
            matches = re.findall(pattern, text)
            
            if matches:
                context = self._get_context(text, skill.lower())
                
                keywords.append({
                    'keyword': skill,
                    'type': 'skill',
                    'frequency': len(matches),
                    'confidence': 0.8,
                    'context': context
                })
        
        return keywords
    
    def _extract_education_keywords(self, text: str) -> List[Dict]:
        """Extract education-related keywords"""
        keywords = []
        
        for keyword in self.education_keywords:
            pattern = r'\b' + re.escape(keyword.lower()) + r'\b'
            matches = re.findall(pattern, text)
            
            if matches:
                context = self._get_context(text, keyword.lower())
                
                keywords.append({
                    'keyword': keyword,
                    'type': 'education',
                    'frequency': len(matches),
                    'confidence': 0.85,
                    'context': context
                })
        
        return keywords
    
    def _extract_experience_keywords(self, text: str) -> List[Dict]:
        """Extract experience-related keywords"""
        keywords = []
        
        for keyword in self.experience_keywords:
            pattern = r'\b' + re.escape(keyword.lower()) + r'\b'
            matches = re.findall(pattern, text)
            
            if matches:
                context = self._get_context(text, keyword.lower())
                
                keywords.append({
                    'keyword': keyword,
                    'type': 'experience',
                    'frequency': len(matches),
                    'confidence': 0.75,
                    'context': context
                })
        
        return keywords
    
    def _extract_nlp_keywords(self, text: str) -> List[Dict]:
        """Extract keywords using NLTK NLP techniques"""
        keywords = []
        
        try:
            # Tokenize text
            tokens = word_tokenize(text)
            
            # Remove stopwords and short words
            filtered_tokens = [
                token for token in tokens 
                if token not in self.stop_words and len(token) > 2 and token.isalpha()
            ]
            
            # Part-of-speech tagging
            pos_tags = pos_tag(filtered_tokens)
            
            # Extract nouns and adjectives (likely to be keywords)
            relevant_pos = ['NN', 'NNS', 'NNP', 'NNPS', 'JJ', 'JJR', 'JJS']
            candidate_keywords = [
                word for word, pos in pos_tags 
                if pos in relevant_pos
            ]
            
            # Count frequency
            keyword_freq = Counter(candidate_keywords)
            
            # Convert to keyword format
            for word, freq in keyword_freq.most_common(20):  # Top 20 keywords
                if freq >= 2:  # Only include words that appear at least twice
                    context = self._get_context(text, word)
                    
                    keywords.append({
                        'keyword': word,
                        'type': 'general',
                        'frequency': freq,
                        'confidence': min(0.7, 0.5 + (freq * 0.1)),
                        'context': context
                    })
        
        except Exception as e:
            self.logger.warning(f"NLP keyword extraction failed: {e}")
        
        return keywords
    
    def _extract_basic_keywords(self, text: str) -> List[Dict]:
        """Basic keyword extraction without NLTK"""
        keywords = []
        
        # Simple word frequency analysis
        words = text.split()
        word_freq = Counter(words)
        
        # Filter out common words and short words
        for word, freq in word_freq.most_common(15):
            if (len(word) > 3 and 
                word not in self.stop_words and 
                freq >= 2 and 
                word.isalpha()):
                
                context = self._get_context(text, word)
                
                keywords.append({
                    'keyword': word,
                    'type': 'general',
                    'frequency': freq,
                    'confidence': 0.6,
                    'context': context
                })
        
        return keywords
    
    def _get_context(self, text: str, keyword: str, context_length: int = 100) -> str:
        """Get context around a keyword"""
        try:
            # Find the first occurrence of the keyword
            index = text.find(keyword.lower())
            if index == -1:
                return ""
            
            # Extract context around the keyword
            start = max(0, index - context_length // 2)
            end = min(len(text), index + len(keyword) + context_length // 2)
            
            context = text[start:end].strip()
            
            # Clean up context
            if start > 0:
                context = "..." + context
            if end < len(text):
                context = context + "..."
            
            return context
            
        except Exception:
            return ""
    
    def _deduplicate_and_rank(self, keywords: List[Dict], original_text: str) -> List[Dict]:
        """Remove duplicates and rank keywords by importance"""
        # Group by keyword (case-insensitive)
        keyword_groups = {}
        
        for kw in keywords:
            key = kw['keyword'].lower()
            if key not in keyword_groups:
                keyword_groups[key] = []
            keyword_groups[key].append(kw)
        
        # Merge duplicates and calculate final scores
        final_keywords = []
        
        for key, group in keyword_groups.items():
            if len(group) == 1:
                final_keywords.append(group[0])
            else:
                # Merge duplicates
                merged = {
                    'keyword': group[0]['keyword'],  # Keep original case
                    'type': group[0]['type'],  # Use first type
                    'frequency': sum(kw['frequency'] for kw in group),
                    'confidence': max(kw['confidence'] for kw in group),
                    'context': group[0]['context']  # Use first context
                }
                final_keywords.append(merged)
        
        # Sort by confidence and frequency
        final_keywords.sort(
            key=lambda x: (x['confidence'], x['frequency']), 
            reverse=True
        )
        
        return final_keywords[:50]  # Return top 50 keywords
    
    def _load_tech_skills(self) -> Set[str]:
        """Load technical skills database"""
        return {
            # Programming Languages
            'Python', 'Java', 'JavaScript', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift',
            'Kotlin', 'TypeScript', 'Scala', 'R', 'MATLAB', 'SQL', 'HTML', 'CSS',
            
            # Frameworks and Libraries
            'React', 'Angular', 'Vue.js', 'Node.js', 'Express.js', 'Django', 'Flask', 'Spring',
            'Laravel', 'Rails', 'jQuery', 'Bootstrap', 'TensorFlow', 'PyTorch', 'Pandas',
            'NumPy', 'Scikit-learn',
            
            # Databases
            'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'SQLite', 'Oracle', 'SQL Server',
            'Elasticsearch', 'Cassandra', 'DynamoDB',
            
            # Cloud and DevOps
            'AWS', 'Azure', 'Google Cloud', 'Docker', 'Kubernetes', 'Jenkins', 'Git', 'GitHub',
            'GitLab', 'CI/CD', 'Terraform', 'Ansible',
            
            # Tools and Technologies
            'Linux', 'Windows', 'macOS', 'Apache', 'Nginx', 'REST API', 'GraphQL', 'Microservices',
            'Agile', 'Scrum', 'JIRA', 'Confluence'
        }
    
    def _load_soft_skills(self) -> Set[str]:
        """Load soft skills database"""
        return {
            'leadership', 'communication', 'teamwork', 'problem solving', 'analytical thinking',
            'creativity', 'adaptability', 'time management', 'project management', 'collaboration',
            'critical thinking', 'decision making', 'negotiation', 'presentation', 'mentoring',
            'coaching', 'strategic planning', 'innovation', 'customer service', 'sales'
        }
    
    def _load_education_keywords(self) -> Set[str]:
        """Load education-related keywords"""
        return {
            'bachelor', 'master', 'phd', 'doctorate', 'degree', 'university', 'college',
            'computer science', 'engineering', 'business administration', 'mba', 'certification',
            'diploma', 'associate', 'graduate', 'undergraduate', 'coursework', 'gpa'
        }
    
    def _load_experience_keywords(self) -> Set[str]:
        """Load experience-related keywords"""
        return {
            'senior', 'junior', 'lead', 'manager', 'director', 'architect', 'developer',
            'engineer', 'analyst', 'consultant', 'specialist', 'coordinator', 'supervisor',
            'executive', 'intern', 'entry level', 'mid level', 'experienced', 'years experience'
        }
    
    def _get_basic_stopwords(self) -> Set[str]:
        """Basic stopwords list when NLTK is not available"""
        return {
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from', 'has', 'he',
            'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the', 'to', 'was', 'will', 'with',
            'the', 'this', 'but', 'they', 'have', 'had', 'what', 'said', 'each', 'which',
            'she', 'do', 'how', 'their', 'if', 'up', 'out', 'many', 'then', 'them', 'these',
            'so', 'some', 'her', 'would', 'make', 'like', 'into', 'him', 'time', 'two', 'more',
            'go', 'no', 'way', 'could', 'my', 'than', 'first', 'been', 'call', 'who', 'oil',
            'sit', 'now', 'find', 'down', 'day', 'did', 'get', 'come', 'made', 'may', 'part'
        }
