/**
 * Dr. Resume - Integrated Frontend Application
 * ==========================================
 * 
 * This JavaScript file handles all frontend functionality for the integrated
 * Dr. Resume application, combining US-01 through US-10 features.
 * 
 * Features:
 * - User authentication (login/register)
 * - Resume upload and management
 * - Job description management
 * - Matching score calculation
 * - Suggestions (basic and premium)
 * - Dashboard and analytics
 * - Account settings
 */

// Global variables
let currentUser = null;
let authToken = null;
let refreshToken = null;

// API base URL
const API_BASE_URL = window.location.origin;

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    checkAuthStatus();
});

/**
 * Initialize the application
 */
function initializeApp() {
    console.log('Initializing Dr. Resume application...');
    
    // Check for stored tokens
    authToken = localStorage.getItem('authToken');
    refreshToken = localStorage.getItem('refreshToken');
    
    if (authToken) {
        // Validate token and get user info
        validateTokenAndGetUser();
    } else {
        showSection('login');
    }
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
    // Login form
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
    
    // Register form
    document.getElementById('registerForm').addEventListener('submit', handleRegister);
    
    // Resume upload form
    document.getElementById('resumeUploadForm').addEventListener('submit', handleResumeUpload);
    
    // File upload drag and drop
    setupFileUpload();
}

/**
 * Setup file upload drag and drop functionality
 */
function setupFileUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('resumeFile');
    
    // Drag and drop events
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            updateFileDisplay(files[0]);
        }
    });
    
    // Click to upload
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
    
    // File input change
    fileInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            updateFileDisplay(this.files[0]);
        }
    });
}

/**
 * Update file display when file is selected
 */
function updateFileDisplay(file) {
    const uploadArea = document.getElementById('uploadArea');
    uploadArea.innerHTML = `
        <i class="fas fa-file-alt fa-3x text-primary mb-3"></i>
        <h5>${file.name}</h5>
        <p class="text-muted">${formatFileSize(file.size)} - ${file.type}</p>
        <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('resumeFile').click()">
            Change File
        </button>
    `;
}

/**
 * Format file size for display
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Check authentication status
 */
function checkAuthStatus() {
    if (authToken) {
        // Show authenticated UI
        document.getElementById('userDropdown').style.display = 'block';
        document.getElementById('loginButton').style.display = 'none';
        
        // Load dashboard if no specific section is shown
        if (!document.querySelector('.section.active') || document.getElementById('loginSection').classList.contains('active')) {
            showSection('dashboard');
        }
    } else {
        // Show login UI
        document.getElementById('userDropdown').style.display = 'none';
        document.getElementById('loginButton').style.display = 'block';
        showSection('login');
    }
}

/**
 * Validate token and get user information
 */
async function validateTokenAndGetUser() {
    try {
        const response = await apiCall('/api/me', 'GET');
        
        if (response.success) {
            currentUser = response.user;
            document.getElementById('userName').textContent = 
                currentUser.first_name || currentUser.email.split('@')[0];
            checkAuthStatus();
            loadDashboardData();
        } else {
            // Token is invalid, clear it
            clearAuthTokens();
            checkAuthStatus();
        }
    } catch (error) {
        console.error('Token validation failed:', error);
        clearAuthTokens();
        checkAuthStatus();
    }
}

/**
 * Handle user login
 */
async function handleLogin(e) {
    e.preventDefault();
    
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;
    
    const submitButton = e.target.querySelector('button[type="submit"]');
    const spinner = submitButton.querySelector('.loading-spinner');
    
    try {
        setLoading(submitButton, spinner, true);
        
        const response = await apiCall('/api/login', 'POST', {
            email: email,
            password: password
        });
        
        if (response.success) {
            // Store tokens
            authToken = response.tokens.access_token;
            refreshToken = response.tokens.refresh_token;
            localStorage.setItem('authToken', authToken);
            localStorage.setItem('refreshToken', refreshToken);
            
            // Store user info
            currentUser = response.user;
            
            showAlert('Login successful!', 'success');
            checkAuthStatus();
            loadDashboardData();
        } else {
            showAlert(response.message || 'Login failed', 'danger');
        }
    } catch (error) {
        console.error('Login error:', error);
        showAlert('Login failed. Please try again.', 'danger');
    } finally {
        setLoading(submitButton, spinner, false);
    }
}

/**
 * Handle user registration
 */
async function handleRegister(e) {
    e.preventDefault();
    
    const firstName = document.getElementById('registerFirstName').value;
    const lastName = document.getElementById('registerLastName').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    
    const submitButton = e.target.querySelector('button[type="submit"]');
    const spinner = submitButton.querySelector('.loading-spinner');
    
    try {
        setLoading(submitButton, spinner, true);
        
        const response = await apiCall('/api/register', 'POST', {
            first_name: firstName,
            last_name: lastName,
            email: email,
            password: password
        });
        
        if (response.success) {
            showAlert('Registration successful! Please login.', 'success');
            showSection('login');
            
            // Pre-fill login form
            document.getElementById('loginEmail').value = email;
        } else {
            showAlert(response.message || 'Registration failed', 'danger');
        }
    } catch (error) {
        console.error('Registration error:', error);
        showAlert('Registration failed. Please try again.', 'danger');
    } finally {
        setLoading(submitButton, spinner, false);
    }
}

/**
 * Handle resume upload
 */
async function handleResumeUpload(e) {
    e.preventDefault();
    
    const fileInput = document.getElementById('resumeFile');
    const title = document.getElementById('resumeTitle').value;
    const description = document.getElementById('resumeDescription').value;
    
    if (!fileInput.files || fileInput.files.length === 0) {
        showAlert('Please select a file to upload', 'warning');
        return;
    }
    
    const file = fileInput.files[0];
    
    // Validate file type
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
        showAlert('Please upload a PDF, DOC, or DOCX file', 'warning');
        return;
    }
    
    // Validate file size (10MB)
    if (file.size > 10 * 1024 * 1024) {
        showAlert('File size must be less than 10MB', 'warning');
        return;
    }
    
    const submitButton = e.target.querySelector('button[type="submit"]');
    const spinner = submitButton.querySelector('.loading-spinner');
    
    try {
        setLoading(submitButton, spinner, true);
        
        const formData = new FormData();
        formData.append('file', file);
        if (title) formData.append('title', title);
        if (description) formData.append('description', description);
        
        const response = await apiCall('/api/upload_resume', 'POST', formData, true);
        
        if (response.success) {
            showAlert('Resume uploaded successfully!', 'success');
            
            // Reset form
            document.getElementById('resumeUploadForm').reset();
            resetUploadArea();
            
            // Refresh resume list
            loadResumeList();
            loadDashboardData();
        } else {
            showAlert(response.message || 'Upload failed', 'danger');
        }
    } catch (error) {
        console.error('Upload error:', error);
        showAlert('Upload failed. Please try again.', 'danger');
    } finally {
        setLoading(submitButton, spinner, false);
    }
}

/**
 * Reset upload area to initial state
 */
function resetUploadArea() {
    const uploadArea = document.getElementById('uploadArea');
    uploadArea.innerHTML = `
        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
        <h5>Drag & Drop your resume here</h5>
        <p class="text-muted">or click to browse files</p>
        <button type="button" class="btn btn-primary" onclick="document.getElementById('resumeFile').click()">
            Choose File
        </button>
    `;
}

/**
 * Load dashboard data
 */
async function loadDashboardData() {
    try {
        // Load analytics
        const analyticsResponse = await apiCall('/api/analytics', 'GET');
        
        if (analyticsResponse.success) {
            const analytics = analyticsResponse.analytics;
            
            // Update stats cards
            document.getElementById('totalResumes').textContent = analytics.overview.total_resumes;
            document.getElementById('totalJobs').textContent = analytics.overview.total_job_descriptions;
            document.getElementById('totalScans').textContent = analytics.overview.total_scans;
            document.getElementById('avgScore').textContent = analytics.matching_scores.average_match_score + '%';
        }
        
        // Load recent scans
        const historyResponse = await apiCall('/api/history?per_page=5', 'GET');
        
        if (historyResponse.success) {
            displayRecentScans(historyResponse.scan_history);
        }
        
    } catch (error) {
        console.error('Failed to load dashboard data:', error);
    }
}

/**
 * Display recent scans
 */
function displayRecentScans(scans) {
    const container = document.getElementById('recentScans');
    
    if (!scans || scans.length === 0) {
        container.innerHTML = '<p class="text-muted">No recent scans found.</p>';
        return;
    }
    
    const scansHtml = scans.map(scan => `
        <div class="d-flex justify-content-between align-items-center border-bottom py-2">
            <div>
                <h6 class="mb-1">${scan.scan_name}</h6>
                <small class="text-muted">${scan.job_title} at ${scan.company_name || 'Unknown Company'}</small>
            </div>
            <div class="text-end">
                <span class="badge ${getScoreBadgeClass(scan.overall_match_score)}">${scan.overall_match_score}%</span>
                <br>
                <small class="text-muted">${formatDate(scan.created_at)}</small>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = scansHtml;
}

/**
 * Get badge class based on score
 */
function getScoreBadgeClass(score) {
    if (score >= 80) return 'bg-success';
    if (score >= 60) return 'bg-info';
    if (score >= 40) return 'bg-warning';
    return 'bg-danger';
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString();
}

/**
 * Load resume list
 */
async function loadResumeList() {
    try {
        const response = await apiCall('/api/resumes', 'GET');
        
        if (response.success) {
            displayResumeList(response.resumes);
        }
    } catch (error) {
        console.error('Failed to load resume list:', error);
    }
}

/**
 * Display resume list
 */
function displayResumeList(resumes) {
    const container = document.getElementById('resumeList');
    
    if (!resumes || resumes.length === 0) {
        container.innerHTML = '<p class="text-muted">No resumes uploaded yet.</p>';
        return;
    }
    
    const resumesHtml = resumes.map(resume => `
        <div class="border-bottom py-2">
            <h6 class="mb-1">${resume.resume_title}</h6>
            <small class="text-muted">${resume.original_filename} - ${formatFileSize(resume.file_size)}</small>
            <br>
            <small class="text-muted">Uploaded: ${formatDate(resume.created_at)}</small>
        </div>
    `).join('');
    
    container.innerHTML = resumesHtml;
}

/**
 * Show a specific section
 */
function showSection(sectionName) {
    // Hide all sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Show the requested section
    const targetSection = document.getElementById(sectionName + 'Section');
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Load section-specific data
    switch (sectionName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'upload':
            loadResumeList();
            break;
    }
}

/**
 * Logout user
 */
async function logout() {
    try {
        // Call logout endpoint
        await apiCall('/api/logout', 'POST');
    } catch (error) {
        console.error('Logout error:', error);
    } finally {
        // Clear tokens and user data
        clearAuthTokens();
        currentUser = null;
        
        // Redirect to login
        showSection('login');
        checkAuthStatus();
        
        showAlert('Logged out successfully', 'info');
    }
}

/**
 * Clear authentication tokens
 */
function clearAuthTokens() {
    authToken = null;
    refreshToken = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
}

/**
 * Make API call with authentication
 */
async function apiCall(endpoint, method = 'GET', data = null, isFormData = false) {
    const url = API_BASE_URL + endpoint;
    
    const options = {
        method: method,
        headers: {}
    };
    
    // Add authentication header if token exists
    if (authToken) {
        options.headers['Authorization'] = `Bearer ${authToken}`;
    }
    
    // Add data to request
    if (data) {
        if (isFormData) {
            options.body = data;
        } else {
            options.headers['Content-Type'] = 'application/json';
            options.body = JSON.stringify(data);
        }
    }
    
    const response = await fetch(url, options);
    
    // Handle token refresh if needed
    if (response.status === 401 && refreshToken) {
        const refreshed = await refreshAuthToken();
        if (refreshed) {
            // Retry the original request
            options.headers['Authorization'] = `Bearer ${authToken}`;
            return fetch(url, options).then(r => r.json());
        }
    }
    
    return response.json();
}

/**
 * Refresh authentication token
 */
async function refreshAuthToken() {
    try {
        const response = await fetch(API_BASE_URL + '/api/refresh', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${refreshToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            authToken = result.tokens.access_token;
            localStorage.setItem('authToken', authToken);
            return true;
        } else {
            clearAuthTokens();
            return false;
        }
    } catch (error) {
        console.error('Token refresh failed:', error);
        clearAuthTokens();
        return false;
    }
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHtml = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHtml);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alertElement = document.getElementById(alertId);
        if (alertElement) {
            alertElement.remove();
        }
    }, 5000);
}

/**
 * Set loading state for buttons
 */
function setLoading(button, spinner, isLoading) {
    if (isLoading) {
        button.disabled = true;
        spinner.classList.add('show');
    } else {
        button.disabled = false;
        spinner.classList.remove('show');
    }
}
