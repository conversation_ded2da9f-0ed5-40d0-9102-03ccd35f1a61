# Dr. Resume - Integrated Application Dependencies
# ===============================================
# 
# This file contains all required Python packages for the integrated
# Dr. Resume application (US-01 through US-10).
#
# Installation: pip install -r requirements.txt

# Core Flask Framework
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-CORS==4.0.0
Flask-JWT-Extended==4.5.3
Flask-Limiter==3.5.0

# Database
SQLAlchemy==2.0.21

# Security and Authentication
Werkzeug==2.3.7
PyJWT==2.8.0
bcrypt==4.0.1

# File Processing
PyPDF2==3.0.1
python-docx==0.8.11

# Natural Language Processing
nltk==3.8.1
scikit-learn==1.3.0

# Optional: Advanced NLP (uncomment if needed)
# spacy==3.6.1

# HTTP Requests and API Integration
requests==2.31.0

# Environment Variables
python-dotenv==1.0.0

# Date and Time Utilities
python-dateutil==2.8.2

# JSON and Data Processing
jsonschema==4.19.1

# File Type Detection (optional)
# python-magic==0.4.27

# OpenAI API for Premium Suggestions (optional)
openai==0.28.1

# Development and Testing (optional)
# pytest==7.4.2
# pytest-flask==1.2.0
# pytest-cov==4.1.0

# Production Server (optional)
# gunicorn==21.2.0
# waitress==2.1.2

# Monitoring and Logging (optional)
# sentry-sdk[flask]==1.32.0

# Additional Utilities
click==8.1.7
itsdangerous==2.1.2
Jinja2==3.1.2
MarkupSafe==2.1.3

# Data Validation
marshmallow==3.20.1

# UUID Generation (built-in, but listed for reference)
# uuid

# File System Operations (built-in)
# os
# pathlib

# Regular Expressions (built-in)
# re

# JSON Processing (built-in)
# json

# Date and Time (built-in)
# datetime

# Collections (built-in)
# collections

# Logging (built-in)
# logging

# Type Hints (built-in in Python 3.5+)
# typing
