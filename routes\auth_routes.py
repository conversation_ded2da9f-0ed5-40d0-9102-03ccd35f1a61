"""
Authentication Routes - US-01 & US-02
=====================================

This module handles user registration, login, and JWT token management.
Combines functionality from US-01 (User Registration) and US-02 (Login + JWT Token).

Routes:
- POST /api/register - User registration
- POST /api/login - User login with JWT
- POST /api/refresh - Refresh JWT token
- POST /api/logout - User logout
- GET /api/me - Get current user profile
- POST /api/check-email - Check email availability
"""

import re
from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, create_access_token, create_refresh_token, get_jwt
from models import db, User, UserProfile

# Create Blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/api')

def validate_email(email):
    """Validate email format using regex"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """
    Validate password strength
    
    Requirements:
    - At least 8 characters
    - At least one uppercase letter
    - At least one lowercase letter
    - At least one digit
    """
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    
    if not re.search(r'[A-Z]', password):
        return False, "Password must contain at least one uppercase letter"
    
    if not re.search(r'[a-z]', password):
        return False, "Password must contain at least one lowercase letter"
    
    if not re.search(r'\d', password):
        return False, "Password must contain at least one digit"
    
    return True, "Password is valid"

@auth_bp.route('/register', methods=['POST'])
def register():
    """
    User Registration Endpoint - US-01
    
    Expected JSON payload:
    {
        "email": "<EMAIL>",
        "password": "securepassword123",
        "first_name": "John",  // optional
        "last_name": "Doe"     // optional
    }
    """
    try:
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Extract required fields
        email = data.get('email', '').strip()
        password = data.get('password', '')
        first_name = data.get('first_name', '').strip()
        last_name = data.get('last_name', '').strip()
        
        # Validate required fields
        if not email or not password:
            return jsonify({
                'success': False,
                'message': 'Email and password are required'
            }), 400
        
        # Validate email format
        if not validate_email(email):
            return jsonify({
                'success': False,
                'message': 'Invalid email format'
            }), 400
        
        # Validate password strength
        is_valid_password, password_error = validate_password(password)
        if not is_valid_password:
            return jsonify({
                'success': False,
                'message': password_error
            }), 400
        
        # Attempt to create user
        user, error = User.create_user(
            email=email,
            password=password,
            first_name=first_name or None,
            last_name=last_name or None
        )
        
        if error:
            return jsonify({
                'success': False,
                'message': error
            }), 400
        
        # Create user profile
        try:
            user_profile = UserProfile(user_id=user.id)
            db.session.add(user_profile)
            db.session.commit()
        except Exception as e:
            print(f"Warning: Could not create user profile: {e}")
        
        # Registration successful
        return jsonify({
            'success': True,
            'message': 'User registered successfully',
            'user': user.to_dict()
        }), 201
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Registration failed: {str(e)}'
        }), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """
    User Login Endpoint - US-02
    
    Expected JSON payload:
    {
        "email": "<EMAIL>",
        "password": "securepassword123"
    }
    """
    try:
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Extract credentials
        email = data.get('email', '').strip().lower()
        password = data.get('password', '')
        
        # Validate required fields
        if not email or not password:
            return jsonify({
                'success': False,
                'message': 'Email and password are required'
            }), 400
        
        # Find user
        user = User.query.filter_by(email=email).first()
        
        if not user or not user.check_password(password):
            return jsonify({
                'success': False,
                'message': 'Invalid email or password'
            }), 401
        
        # Check if user is active
        if not user.is_active:
            return jsonify({
                'success': False,
                'message': 'Account is deactivated'
            }), 401
        
        # Update last login timestamp
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        # Create JWT tokens
        access_token = create_access_token(
            identity=user.id,
            additional_claims={
                'email': user.email,
                'role': user.role,
                'is_premium': user.is_premium
            }
        )
        
        refresh_token = create_refresh_token(identity=user.id)
        
        # Login successful
        return jsonify({
            'success': True,
            'message': 'Login successful',
            'user': user.to_dict(),
            'tokens': {
                'access_token': access_token,
                'refresh_token': refresh_token,
                'token_type': 'Bearer',
                'expires_in': 3600  # 1 hour in seconds
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Login failed: {str(e)}'
        }), 500

@auth_bp.route('/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """
    Refresh JWT Token Endpoint - US-02
    
    Requires a valid refresh token in Authorization header
    """
    try:
        # Get current user identity from refresh token
        current_user_id = get_jwt_identity()
        
        # Find user to ensure they still exist and are active
        user = User.query.get(current_user_id)
        
        if not user or not user.is_active:
            return jsonify({
                'success': False,
                'message': 'User not found or account deactivated'
            }), 401
        
        # Create new access token
        new_access_token = create_access_token(
            identity=user.id,
            additional_claims={
                'email': user.email,
                'role': user.role,
                'is_premium': user.is_premium
            }
        )
        
        return jsonify({
            'success': True,
            'message': 'Token refreshed successfully',
            'tokens': {
                'access_token': new_access_token,
                'token_type': 'Bearer',
                'expires_in': 3600
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Token refresh failed: {str(e)}'
        }), 500

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """
    User Logout Endpoint - US-02
    
    Note: In a production environment, you would add the token to a blacklist
    """
    try:
        # In a real implementation, you would add the token to a blacklist
        # For now, we just return a success message
        
        return jsonify({
            'success': True,
            'message': 'Logout successful'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Logout failed: {str(e)}'
        }), 500

@auth_bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    """
    Get Current User Profile Endpoint - US-02
    
    Returns current user information based on JWT token
    """
    try:
        # Get current user identity
        current_user_id = get_jwt_identity()
        
        # Find user
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        return jsonify({
            'success': True,
            'user': user.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get user profile: {str(e)}'
        }), 500

@auth_bp.route('/check-email', methods=['POST'])
def check_email():
    """
    Check Email Availability Endpoint - US-01
    
    Expected JSON payload:
    {
        "email": "<EMAIL>"
    }
    """
    try:
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        email = data.get('email', '').strip().lower()
        
        if not email:
            return jsonify({
                'success': False,
                'message': 'Email is required'
            }), 400
        
        # Validate email format
        if not validate_email(email):
            return jsonify({
                'success': False,
                'message': 'Invalid email format'
            }), 400
        
        # Check if email exists
        existing_user = User.query.filter_by(email=email).first()
        
        return jsonify({
            'success': True,
            'available': existing_user is None,
            'message': 'Email is available' if existing_user is None else 'Email is already registered'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Email check failed: {str(e)}'
        }), 500
