#!/usr/bin/env python3
"""
Dr. Resume - Integration Test Script
===================================

This script tests the integration of all US-01 to US-10 components
to ensure they work together correctly.

Usage:
    python test_integration.py
"""

import os
import sys
import json
import time
import requests
from pathlib import Path

# Test configuration
BASE_URL = "http://localhost:5000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "TestPassword123!"

class IntegrationTester:
    def __init__(self):
        self.base_url = BASE_URL
        self.session = requests.Session()
        self.auth_token = None
        self.test_user_id = None
        self.test_resume_id = None
        self.test_jd_id = None
        self.test_match_id = None
        
    def run_all_tests(self):
        """Run all integration tests"""
        print("🧪 Dr. Resume Integration Test Suite")
        print("=" * 50)
        
        try:
            # Test server availability
            self.test_server_health()
            
            # Test US-01: User Registration
            self.test_user_registration()
            
            # Test US-02: User Login & JWT
            self.test_user_login()
            
            # Test US-03: Resume Upload
            self.test_resume_upload()
            
            # Test US-04: Job Description Upload
            self.test_job_description_upload()
            
            # Test US-05 & US-06: Keyword Parsing & Matching
            self.test_matching_calculation()
            
            # Test US-07: Suggestions
            self.test_suggestions()
            
            # Test US-08: Dashboard & History
            self.test_dashboard()
            
            # Test US-10: Account Management
            self.test_account_management()
            
            print("\n✅ All integration tests passed!")
            print("🎉 Dr. Resume application is fully integrated and functional!")
            
        except Exception as e:
            print(f"\n❌ Integration test failed: {e}")
            sys.exit(1)
    
    def test_server_health(self):
        """Test server health and availability"""
        print("\n🔍 Testing server health...")
        
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Server is running: {data['message']}")
                print(f"   Database: {data['status']['database']}")
                print(f"   Upload directory: {data['status']['upload_directory']}")
            else:
                raise Exception(f"Health check failed: {response.status_code}")
        except requests.exceptions.ConnectionError:
            raise Exception("Cannot connect to server. Please start the application first.")
    
    def test_user_registration(self):
        """Test US-01: User Registration"""
        print("\n👤 Testing US-01: User Registration...")
        
        # First, try to delete existing test user (ignore errors)
        try:
            self.session.post(f"{self.base_url}/api/login", json={
                "email": TEST_EMAIL,
                "password": TEST_PASSWORD
            })
        except:
            pass
        
        # Register new user
        response = self.session.post(f"{self.base_url}/api/register", json={
            "email": TEST_EMAIL,
            "password": TEST_PASSWORD,
            "first_name": "Test",
            "last_name": "User"
        })
        
        if response.status_code == 201:
            data = response.json()
            print(f"✅ User registration successful: {data['user']['email']}")
            self.test_user_id = data['user']['id']
        elif response.status_code == 400 and "already registered" in response.json().get('message', ''):
            print("✅ User already exists (expected for repeated tests)")
        else:
            raise Exception(f"Registration failed: {response.json()}")
    
    def test_user_login(self):
        """Test US-02: User Login & JWT"""
        print("\n🔐 Testing US-02: User Login & JWT...")
        
        response = self.session.post(f"{self.base_url}/api/login", json={
            "email": TEST_EMAIL,
            "password": TEST_PASSWORD
        })
        
        if response.status_code == 200:
            data = response.json()
            self.auth_token = data['tokens']['access_token']
            self.session.headers.update({
                'Authorization': f"Bearer {self.auth_token}"
            })
            print(f"✅ Login successful, JWT token received")
            print(f"   User: {data['user']['email']}")
        else:
            raise Exception(f"Login failed: {response.json()}")
        
        # Test protected endpoint
        response = self.session.get(f"{self.base_url}/api/me")
        if response.status_code == 200:
            print("✅ JWT authentication working")
        else:
            raise Exception("JWT authentication failed")
    
    def test_resume_upload(self):
        """Test US-03: Resume Upload"""
        print("\n📄 Testing US-03: Resume Upload...")
        
        # Create a test resume file
        test_resume_content = """
        John Doe
        Software Engineer
        
        Experience:
        - Python development
        - Flask framework
        - Machine learning
        - Data analysis
        
        Skills:
        Python, JavaScript, SQL, Git, Docker
        
        Education:
        Bachelor of Computer Science
        """
        
        # Create temporary file
        test_file_path = Path("test_resume.txt")
        test_file_path.write_text(test_resume_content)
        
        try:
            with open(test_file_path, 'rb') as f:
                files = {'file': ('test_resume.txt', f, 'text/plain')}
                data = {
                    'title': 'Test Resume',
                    'description': 'Integration test resume'
                }
                
                response = self.session.post(
                    f"{self.base_url}/api/upload_resume",
                    files=files,
                    data=data
                )
            
            if response.status_code == 201:
                data = response.json()
                self.test_resume_id = data['resume']['id']
                print(f"✅ Resume upload successful: {data['resume']['resume_title']}")
                print(f"   Extraction status: {data['resume']['extraction_status']}")
            else:
                raise Exception(f"Resume upload failed: {response.json()}")
                
        finally:
            # Clean up test file
            if test_file_path.exists():
                test_file_path.unlink()
    
    def test_job_description_upload(self):
        """Test US-04: Job Description Upload"""
        print("\n💼 Testing US-04: Job Description Upload...")
        
        job_description = {
            "title": "Senior Python Developer",
            "company_name": "Tech Corp",
            "job_description_text": """
            We are looking for a Senior Python Developer with experience in:
            - Python programming
            - Flask or Django framework
            - Machine learning and data science
            - SQL databases
            - Git version control
            - Docker containerization
            - Agile development
            
            Requirements:
            - Bachelor's degree in Computer Science
            - 3+ years of Python experience
            - Strong problem-solving skills
            - Team collaboration
            """,
            "location": "San Francisco, CA",
            "employment_type": "full-time",
            "experience_level": "senior-level"
        }
        
        response = self.session.post(
            f"{self.base_url}/api/upload_jd",
            json=job_description
        )
        
        if response.status_code == 201:
            data = response.json()
            self.test_jd_id = data['job_description']['id']
            print(f"✅ Job description upload successful: {data['job_description']['title']}")
            print(f"   Keywords extracted: {data['job_description']['keywords_extracted']}")
        else:
            raise Exception(f"Job description upload failed: {response.json()}")
    
    def test_matching_calculation(self):
        """Test US-05 & US-06: Keyword Parsing & Matching"""
        print("\n🔍 Testing US-05 & US-06: Keyword Parsing & Matching...")
        
        if not self.test_resume_id or not self.test_jd_id:
            raise Exception("Resume or Job Description not available for matching")
        
        match_request = {
            "resume_id": self.test_resume_id,
            "job_description_id": self.test_jd_id,
            "calculation_method": "jaccard"
        }
        
        response = self.session.post(
            f"{self.base_url}/api/calculate_match",
            json=match_request
        )
        
        if response.status_code == 201:
            data = response.json()
            self.test_match_id = data['matching_score']['id']
            match_score = data['matching_score']['overall_match_percentage']
            print(f"✅ Matching calculation successful: {match_score}%")
            print(f"   Matched keywords: {len(data['matching_score']['matched_keywords'])}")
            print(f"   Missing keywords: {len(data['matching_score']['missing_keywords'])}")
        else:
            raise Exception(f"Matching calculation failed: {response.json()}")
    
    def test_suggestions(self):
        """Test US-07: Suggestions"""
        print("\n💡 Testing US-07: Suggestions...")
        
        if not self.test_resume_id or not self.test_jd_id:
            raise Exception("Resume or Job Description not available for suggestions")
        
        # Test basic suggestions
        response = self.session.get(
            f"{self.base_url}/api/suggestions/{self.test_resume_id}/{self.test_jd_id}"
        )
        
        if response.status_code == 200:
            data = response.json()
            suggestions_count = len(data['suggestions'])
            print(f"✅ Basic suggestions generated: {suggestions_count} suggestions")
            
            if suggestions_count > 0:
                print(f"   First suggestion: {data['suggestions'][0]['title']}")
        else:
            raise Exception(f"Suggestions generation failed: {response.json()}")
    
    def test_dashboard(self):
        """Test US-08: Dashboard & History"""
        print("\n📊 Testing US-08: Dashboard & History...")
        
        # Test analytics
        response = self.session.get(f"{self.base_url}/api/analytics")
        
        if response.status_code == 200:
            data = response.json()
            analytics = data['analytics']
            print(f"✅ Analytics retrieved successfully")
            print(f"   Total resumes: {analytics['overview']['total_resumes']}")
            print(f"   Total job descriptions: {analytics['overview']['total_job_descriptions']}")
            print(f"   Total scans: {analytics['overview']['total_scans']}")
        else:
            raise Exception(f"Analytics retrieval failed: {response.json()}")
        
        # Test scan history
        response = self.session.get(f"{self.base_url}/api/history")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Scan history retrieved: {len(data['scan_history'])} entries")
        else:
            raise Exception(f"Scan history retrieval failed: {response.json()}")
    
    def test_account_management(self):
        """Test US-10: Account Management"""
        print("\n⚙️ Testing US-10: Account Management...")
        
        # Test profile retrieval
        response = self.session.get(f"{self.base_url}/api/account/profile")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Profile retrieved successfully")
            print(f"   User email: {data['profile']['user']['email']}")
        else:
            raise Exception(f"Profile retrieval failed: {response.json()}")
        
        # Test profile update
        profile_update = {
            "user": {
                "first_name": "Test Updated"
            },
            "profile": {
                "display_name": "Test User Updated",
                "bio": "Integration test user"
            }
        }
        
        response = self.session.put(
            f"{self.base_url}/api/account/profile",
            json=profile_update
        )
        
        if response.status_code == 200:
            print("✅ Profile update successful")
        else:
            raise Exception(f"Profile update failed: {response.json()}")

def main():
    """Main function"""
    print("🚀 Starting Dr. Resume Integration Tests...")
    print("Make sure the application is running on http://localhost:5000")
    print()
    
    # Wait a moment for user to read
    time.sleep(2)
    
    tester = IntegrationTester()
    tester.run_all_tests()

if __name__ == '__main__':
    main()
