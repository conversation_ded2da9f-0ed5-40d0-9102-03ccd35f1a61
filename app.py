"""
Dr. Resume - Integrated Flask Application
========================================

Main Flask application integrating all US-01 to US-10 functionality:
- US-01: User Registration
- US-02: Login + JWT Token
- US-03: Resume Upload
- US-04: Job Description Upload
- US-05: Keyword Parsing
- US-06: Matching Score
- US-07: Suggestions (Basic + Premium)
- US-08: Dashboard + History
- US-09: API Protection
- US-10: Account Settings

Tech Stack: Flask, SQLite, JWT, CORS, File Upload, NLP
"""

import os
import logging
from datetime import datetime, timedelta
from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS
from flask_jwt_extended import JWTManager, create_access_token, create_refresh_token, jwt_required, get_jwt_identity, get_jwt
from dotenv import load_dotenv

# Import models and database
from models import db, User, Resume, JobDescription, Keyword, MatchingScore, Suggestion, ScanHistory, UserProfile

# Load environment variables
load_dotenv()

def create_app(config_name='development'):
    """
    Create and configure the Flask application
    
    Args:
        config_name (str): Configuration environment (development, production, testing)
        
    Returns:
        Flask: Configured Flask application
    """
    
    # Create Flask app
    app = Flask(__name__)
    
    # Configure CORS for frontend communication
    CORS(app, origins=[
        'http://localhost:3000',
        'http://127.0.0.1:3000',
        'http://localhost:5000',
        'http://127.0.0.1:5000'
    ])
    
    # Database Configuration
    if config_name == 'testing':
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///test_dr_resume.db'
    else:
        # SQLite for now (can be changed to PostgreSQL later)
        db_path = os.getenv('DATABASE_URL', 'sqlite:///dr_resume.db')
        app.config['SQLALCHEMY_DATABASE_URI'] = db_path
    
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # Security Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dr-resume-secret-key-change-in-production')
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'dr-resume-jwt-secret-key')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
    app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
    
    # File Upload Configuration
    upload_folder = os.getenv('UPLOAD_FOLDER', os.path.join(os.path.dirname(__file__), 'uploads'))
    app.config['UPLOAD_FOLDER'] = os.path.abspath(upload_folder)
    app.config['MAX_CONTENT_LENGTH'] = int(os.getenv('MAX_CONTENT_LENGTH', 10 * 1024 * 1024))  # 10MB
    
    # OpenAI Configuration (for premium suggestions)
    app.config['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY', '')
    
    # Initialize extensions
    db.init_app(app)
    jwt = JWTManager(app)
    
    # JWT Configuration
    @jwt.token_in_blocklist_loader
    def check_if_token_revoked(jwt_header, jwt_payload):
        """Check if JWT token is revoked (placeholder for future implementation)"""
        return False
    
    @jwt.additional_claims_loader
    def add_claims_to_jwt(identity):
        """Add additional claims to JWT token"""
        user = User.query.get(identity)
        if user:
            return {
                'email': user.email,
                'role': user.role,
                'is_premium': user.is_premium,
                'is_active': user.is_active
            }
        return {}
    
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        """Handle expired token"""
        return jsonify({
            'success': False,
            'message': 'Token has expired',
            'error': 'token_expired'
        }), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        """Handle invalid token"""
        return jsonify({
            'success': False,
            'message': 'Invalid token',
            'error': 'invalid_token'
        }), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        """Handle missing token"""
        return jsonify({
            'success': False,
            'message': 'Authorization token required',
            'error': 'authorization_required'
        }), 401
    
    # Create database tables and upload directory
    with app.app_context():
        try:
            # Create all database tables
            db.create_all()
            print("✅ Database tables created successfully")
            
            # Ensure upload directory exists
            os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
            print(f"✅ Upload directory ready: {app.config['UPLOAD_FOLDER']}")
            
        except Exception as e:
            print(f"❌ Error during initialization: {e}")
    
    # Configure logging
    if not app.debug:
        logging.basicConfig(level=logging.INFO)
    
    # Root endpoint
    @app.route('/')
    def home():
        """Home endpoint with API information"""
        return jsonify({
            'success': True,
            'message': 'Dr. Resume - AI-Powered Resume Scanner API',
            'version': '1.0.0',
            'description': 'Integrated application combining US-01 through US-10',
            'features': {
                'user_registration': 'US-01',
                'jwt_authentication': 'US-02',
                'resume_upload': 'US-03',
                'job_description_upload': 'US-04',
                'keyword_parsing': 'US-05',
                'matching_score': 'US-06',
                'suggestions': 'US-07',
                'dashboard_history': 'US-08',
                'api_protection': 'US-09',
                'account_settings': 'US-10'
            },
            'endpoints': {
                'authentication': {
                    'register': 'POST /api/register',
                    'login': 'POST /api/login',
                    'refresh': 'POST /api/refresh',
                    'logout': 'POST /api/logout',
                    'profile': 'GET /api/me'
                },
                'resume_management': {
                    'upload': 'POST /api/upload_resume',
                    'list': 'GET /api/resumes',
                    'details': 'GET /api/resumes/<id>',
                    'delete': 'DELETE /api/resumes/<id>'
                },
                'job_descriptions': {
                    'create': 'POST /api/upload_jd',
                    'list': 'GET /api/job_descriptions',
                    'details': 'GET /api/job_descriptions/<id>',
                    'delete': 'DELETE /api/job_descriptions/<id>'
                },
                'analysis': {
                    'calculate_match': 'POST /api/calculate_match',
                    'get_suggestions': 'GET /api/suggestions/<resume_id>/<jd_id>',
                    'premium_suggestions': 'POST /api/premium_suggestions'
                },
                'dashboard': {
                    'history': 'GET /api/history',
                    'analytics': 'GET /api/analytics'
                },
                'account': {
                    'profile': 'GET /api/account/profile',
                    'update_profile': 'PUT /api/account/profile',
                    'change_password': 'PUT /api/account/password'
                }
            },
            'database': 'SQLite (configurable to PostgreSQL)',
            'upload_config': {
                'max_file_size': '10MB',
                'supported_formats': ['PDF', 'DOC', 'DOCX'],
                'upload_folder': app.config['UPLOAD_FOLDER']
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Health check endpoint
    @app.route('/health')
    def health_check():
        """Health check endpoint"""
        try:
            # Test database connection
            db.session.execute('SELECT 1')
            db_status = 'connected'
            
            # Count records
            user_count = User.query.count()
            resume_count = Resume.query.count()
            jd_count = JobDescription.query.count()
            
        except Exception as e:
            db_status = f'error: {str(e)}'
            user_count = resume_count = jd_count = 'unknown'
        
        # Check upload directory
        upload_dir_status = 'ready' if os.path.exists(app.config['UPLOAD_FOLDER']) else 'not_found'
        
        return jsonify({
            'success': True,
            'message': 'Dr. Resume API is running',
            'status': {
                'app': 'running',
                'database': db_status,
                'upload_directory': upload_dir_status,
                'jwt': 'configured'
            },
            'statistics': {
                'total_users': user_count,
                'total_resumes': resume_count,
                'total_job_descriptions': jd_count
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    # Import and register route blueprints
    from routes.auth_routes import auth_bp
    from routes.resume_routes import resume_bp
    from routes.job_description_routes import jd_bp
    from routes.analysis_routes import analysis_bp
    from routes.dashboard_routes import dashboard_bp
    from routes.account_routes import account_bp
    
    # Register blueprints
    app.register_blueprint(auth_bp)
    app.register_blueprint(resume_bp)
    app.register_blueprint(jd_bp)
    app.register_blueprint(analysis_bp)
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(account_bp)
    
    # Serve static files
    @app.route('/static/<path:filename>')
    def serve_static(filename):
        """Serve static files"""
        return send_from_directory('static', filename)
    
    # Global error handlers
    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 errors"""
        return jsonify({
            'success': False,
            'message': 'API endpoint not found',
            'error': 'Not Found'
        }), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """Handle 500 errors"""
        return jsonify({
            'success': False,
            'message': 'Internal server error',
            'error': 'Internal Server Error'
        }), 500
    
    @app.errorhandler(413)
    def file_too_large(error):
        """Handle file too large errors"""
        return jsonify({
            'success': False,
            'message': 'File too large. Maximum size is 10MB.',
            'error': 'Payload Too Large'
        }), 413
    
    return app

if __name__ == '__main__':
    app = create_app('development')
    
    print("🚀 Starting Dr. Resume Integrated Application...")
    print("📋 Features: US-01 to US-10 (Registration → Premium Suggestions)")
    print("🌐 Server: http://localhost:5000")
    print("📚 API Documentation: http://localhost:5000")
    print("🔍 Health Check: http://localhost:5000/health")
    print("🎯 Frontend: http://localhost:5000/static/index.html")
    print("\n💡 Make sure to install dependencies: pip install -r requirements.txt\n")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=True
    )
