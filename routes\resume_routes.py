"""
Resume Routes - US-03
=====================

This module handles resume upload, file processing, and management.
Implements US-03 (Resume Upload) functionality.

Routes:
- POST /api/upload_resume - Upload resume file
- GET /api/resumes - List user's resumes
- GET /api/resumes/<id> - Get resume details
- DELETE /api/resumes/<id> - Delete resume
- POST /api/resumes/<id>/reprocess - Reprocess resume text
"""

import os
import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
from models import db, Resume, Keyword
from services.file_processor import FileProcessor
from services.keyword_extractor import KeywordExtractor

# Create Blueprint
resume_bp = Blueprint('resume', __name__, url_prefix='/api')

# Allowed file extensions
ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx'}

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_file_type(filename):
    """Get MIME type based on file extension"""
    ext = filename.rsplit('.', 1)[1].lower()
    mime_types = {
        'pdf': 'application/pdf',
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    }
    return mime_types.get(ext, 'application/octet-stream')

@resume_bp.route('/upload_resume', methods=['POST'])
@jwt_required()
def upload_resume():
    """
    Resume Upload Endpoint - US-03
    
    Handles file upload, validation, storage, and text extraction.
    
    Expected form data:
    - file: Resume file (PDF/DOC/DOCX)
    - title: Optional resume title
    - description: Optional resume description
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': 'No file provided'
            }), 400
        
        file = request.files['file']
        
        # Check if file is selected
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': 'No file selected'
            }), 400
        
        # Validate file type
        if not allowed_file(file.filename):
            return jsonify({
                'success': False,
                'message': 'Invalid file type. Only PDF, DOC, and DOCX files are allowed.'
            }), 400
        
        # Get optional metadata
        resume_title = request.form.get('title', '').strip()
        resume_description = request.form.get('description', '').strip()
        
        # Generate secure filename
        original_filename = secure_filename(file.filename)
        file_extension = original_filename.rsplit('.', 1)[1].lower()
        stored_filename = f"{uuid.uuid4()}.{file_extension}"
        
        # Create upload directory if it doesn't exist
        upload_folder = current_app.config['UPLOAD_FOLDER']
        os.makedirs(upload_folder, exist_ok=True)
        
        # Save file
        file_path = os.path.join(upload_folder, stored_filename)
        file.save(file_path)
        
        # Get file size
        file_size = os.path.getsize(file_path)
        file_type = get_file_type(original_filename)
        
        # Create resume record
        resume = Resume(
            user_id=current_user_id,
            original_filename=original_filename,
            stored_filename=stored_filename,
            file_path=file_path,
            file_size=file_size,
            file_type=file_type,
            file_extension=file_extension,
            resume_title=resume_title or original_filename,
            resume_description=resume_description
        )
        
        db.session.add(resume)
        db.session.commit()
        
        # Process file asynchronously (extract text and keywords)
        try:
            # Extract text from file
            file_processor = FileProcessor()
            extracted_text = file_processor.extract_text(file_path, file_type)
            
            if extracted_text:
                resume.extracted_text = extracted_text
                resume.extraction_status = 'success'
                resume.upload_status = 'processed'
                
                # Extract keywords
                keyword_extractor = KeywordExtractor()
                keywords = keyword_extractor.extract_keywords(extracted_text, 'resume')
                
                # Save keywords to database
                for keyword_data in keywords:
                    keyword = Keyword(
                        user_id=current_user_id,
                        resume_id=resume.id,
                        keyword=keyword_data['keyword'],
                        keyword_type=keyword_data.get('type', 'general'),
                        frequency=keyword_data.get('frequency', 1),
                        confidence_score=keyword_data.get('confidence', 0.8),
                        context_snippet=keyword_data.get('context', ''),
                        extraction_method='nltk_spacy'
                    )
                    db.session.add(keyword)
                
                db.session.commit()
                
            else:
                resume.extraction_status = 'failed'
                resume.extraction_error = 'Could not extract text from file'
                db.session.commit()
                
        except Exception as e:
            resume.extraction_status = 'failed'
            resume.extraction_error = str(e)
            db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Resume uploaded successfully',
            'resume': resume.to_dict()
        }), 201
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Upload failed: {str(e)}'
        }), 500

@resume_bp.route('/resumes', methods=['GET'])
@jwt_required()
def list_resumes():
    """
    List User's Resumes Endpoint - US-03
    
    Returns all resumes for the current user
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        # Query resumes
        resumes_query = Resume.query.filter_by(
            user_id=current_user_id,
            is_active=True
        ).order_by(Resume.created_at.desc())
        
        # Paginate results
        resumes_paginated = resumes_query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        return jsonify({
            'success': True,
            'resumes': [resume.to_dict() for resume in resumes_paginated.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': resumes_paginated.total,
                'pages': resumes_paginated.pages,
                'has_next': resumes_paginated.has_next,
                'has_prev': resumes_paginated.has_prev
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to list resumes: {str(e)}'
        }), 500

@resume_bp.route('/resumes/<resume_id>', methods=['GET'])
@jwt_required()
def get_resume_details(resume_id):
    """
    Get Resume Details Endpoint - US-03
    
    Returns detailed information about a specific resume
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find resume
        resume = Resume.query.filter_by(
            id=resume_id,
            user_id=current_user_id,
            is_active=True
        ).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        # Get associated keywords
        keywords = Keyword.query.filter_by(resume_id=resume_id).all()
        
        resume_data = resume.to_dict()
        resume_data['keywords'] = [keyword.to_dict() for keyword in keywords]
        
        return jsonify({
            'success': True,
            'resume': resume_data
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get resume details: {str(e)}'
        }), 500

@resume_bp.route('/resumes/<resume_id>', methods=['DELETE'])
@jwt_required()
def delete_resume(resume_id):
    """
    Delete Resume Endpoint - US-03
    
    Soft deletes a resume (marks as inactive)
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find resume
        resume = Resume.query.filter_by(
            id=resume_id,
            user_id=current_user_id,
            is_active=True
        ).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        # Soft delete (mark as inactive)
        resume.is_active = False
        resume.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Resume deleted successfully'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to delete resume: {str(e)}'
        }), 500

@resume_bp.route('/resumes/<resume_id>/reprocess', methods=['POST'])
@jwt_required()
def reprocess_resume(resume_id):
    """
    Reprocess Resume Endpoint - US-03
    
    Re-extracts text and keywords from the resume file
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find resume
        resume = Resume.query.filter_by(
            id=resume_id,
            user_id=current_user_id,
            is_active=True
        ).first()
        
        if not resume:
            return jsonify({
                'success': False,
                'message': 'Resume not found'
            }), 404
        
        # Check if file exists
        if not os.path.exists(resume.file_path):
            return jsonify({
                'success': False,
                'message': 'Resume file not found on disk'
            }), 404
        
        # Reprocess file
        try:
            # Extract text from file
            file_processor = FileProcessor()
            extracted_text = file_processor.extract_text(resume.file_path, resume.file_type)
            
            if extracted_text:
                resume.extracted_text = extracted_text
                resume.extraction_status = 'success'
                resume.extraction_error = None
                resume.updated_at = datetime.utcnow()
                
                # Delete existing keywords
                Keyword.query.filter_by(resume_id=resume_id).delete()
                
                # Extract new keywords
                keyword_extractor = KeywordExtractor()
                keywords = keyword_extractor.extract_keywords(extracted_text, 'resume')
                
                # Save new keywords to database
                for keyword_data in keywords:
                    keyword = Keyword(
                        user_id=current_user_id,
                        resume_id=resume.id,
                        keyword=keyword_data['keyword'],
                        keyword_type=keyword_data.get('type', 'general'),
                        frequency=keyword_data.get('frequency', 1),
                        confidence_score=keyword_data.get('confidence', 0.8),
                        context_snippet=keyword_data.get('context', ''),
                        extraction_method='nltk_spacy'
                    )
                    db.session.add(keyword)
                
                db.session.commit()
                
                return jsonify({
                    'success': True,
                    'message': 'Resume reprocessed successfully',
                    'resume': resume.to_dict()
                }), 200
                
            else:
                resume.extraction_status = 'failed'
                resume.extraction_error = 'Could not extract text from file'
                db.session.commit()
                
                return jsonify({
                    'success': False,
                    'message': 'Failed to extract text from resume'
                }), 500
                
        except Exception as e:
            resume.extraction_status = 'failed'
            resume.extraction_error = str(e)
            db.session.commit()
            
            return jsonify({
                'success': False,
                'message': f'Reprocessing failed: {str(e)}'
            }), 500
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to reprocess resume: {str(e)}'
        }), 500
