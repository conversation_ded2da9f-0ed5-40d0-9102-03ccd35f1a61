"""
File Processor Service - US-03
==============================

This service handles file processing for resume uploads.
Extracts text from PDF, DOC, and DOCX files.

Tech Stack: PyPDF2, python-docx, python-magic (optional)
"""

import os
import logging
from typing import Optional

try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    print("Warning: PyPDF2 not installed. PDF processing will not work.")

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("Warning: python-docx not installed. DOCX processing will not work.")

class FileProcessor:
    """
    File processor for extracting text from various document formats
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def extract_text(self, file_path: str, file_type: str) -> Optional[str]:
        """
        Extract text from a file based on its type
        
        Args:
            file_path (str): Path to the file
            file_type (str): MIME type of the file
            
        Returns:
            Optional[str]: Extracted text or None if extraction failed
        """
        try:
            if not os.path.exists(file_path):
                self.logger.error(f"File not found: {file_path}")
                return None
            
            # Determine extraction method based on file type
            if file_type == 'application/pdf':
                return self._extract_pdf_text(file_path)
            elif file_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                return self._extract_docx_text(file_path)
            elif file_type == 'application/msword':
                return self._extract_doc_text(file_path)
            else:
                self.logger.error(f"Unsupported file type: {file_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error extracting text from {file_path}: {str(e)}")
            return None
    
    def _extract_pdf_text(self, file_path: str) -> Optional[str]:
        """
        Extract text from PDF file using PyPDF2
        
        Args:
            file_path (str): Path to PDF file
            
        Returns:
            Optional[str]: Extracted text or None if extraction failed
        """
        if not PDF_AVAILABLE:
            self.logger.error("PyPDF2 not available for PDF processing")
            return None
        
        try:
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Extract text from all pages
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text += page.extract_text() + "\n"
            
            # Clean up the text
            text = self._clean_text(text)
            
            if not text.strip():
                self.logger.warning(f"No text extracted from PDF: {file_path}")
                return None
            
            return text
            
        except Exception as e:
            self.logger.error(f"Error extracting PDF text: {str(e)}")
            return None
    
    def _extract_docx_text(self, file_path: str) -> Optional[str]:
        """
        Extract text from DOCX file using python-docx
        
        Args:
            file_path (str): Path to DOCX file
            
        Returns:
            Optional[str]: Extracted text or None if extraction failed
        """
        if not DOCX_AVAILABLE:
            self.logger.error("python-docx not available for DOCX processing")
            return None
        
        try:
            doc = Document(file_path)
            text = ""
            
            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text += cell.text + " "
                    text += "\n"
            
            # Clean up the text
            text = self._clean_text(text)
            
            if not text.strip():
                self.logger.warning(f"No text extracted from DOCX: {file_path}")
                return None
            
            return text
            
        except Exception as e:
            self.logger.error(f"Error extracting DOCX text: {str(e)}")
            return None
    
    def _extract_doc_text(self, file_path: str) -> Optional[str]:
        """
        Extract text from DOC file
        
        Note: This is a basic implementation. For better DOC support,
        consider using python-docx2txt or antiword
        
        Args:
            file_path (str): Path to DOC file
            
        Returns:
            Optional[str]: Extracted text or None if extraction failed
        """
        try:
            # Try to read as plain text (fallback method)
            with open(file_path, 'rb') as file:
                content = file.read()
                
            # Basic text extraction (this is very limited for DOC files)
            # In a production environment, you would use a proper DOC parser
            text = content.decode('utf-8', errors='ignore')
            text = self._clean_text(text)
            
            # Filter out binary content (very basic approach)
            printable_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789 \n\t.,!?;:()[]{}"-\'')
            filtered_text = ''.join(char for char in text if char in printable_chars)
            
            if len(filtered_text.strip()) < 50:  # Too little text, probably failed
                self.logger.warning(f"Insufficient text extracted from DOC: {file_path}")
                return None
            
            return filtered_text
            
        except Exception as e:
            self.logger.error(f"Error extracting DOC text: {str(e)}")
            return None
    
    def _clean_text(self, text: str) -> str:
        """
        Clean and normalize extracted text
        
        Args:
            text (str): Raw extracted text
            
        Returns:
            str: Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line:  # Skip empty lines
                cleaned_lines.append(line)
        
        # Join lines with single newlines
        cleaned_text = '\n'.join(cleaned_lines)
        
        # Remove excessive spaces
        import re
        cleaned_text = re.sub(r' +', ' ', cleaned_text)
        
        return cleaned_text
    
    def get_file_info(self, file_path: str) -> dict:
        """
        Get basic information about a file
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            dict: File information
        """
        try:
            if not os.path.exists(file_path):
                return {'error': 'File not found'}
            
            stat = os.stat(file_path)
            
            return {
                'size': stat.st_size,
                'created': stat.st_ctime,
                'modified': stat.st_mtime,
                'exists': True
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def validate_file(self, file_path: str, max_size_mb: int = 10) -> tuple[bool, str]:
        """
        Validate a file for processing
        
        Args:
            file_path (str): Path to the file
            max_size_mb (int): Maximum file size in MB
            
        Returns:
            tuple[bool, str]: (is_valid, error_message)
        """
        try:
            if not os.path.exists(file_path):
                return False, "File not found"
            
            # Check file size
            file_size = os.path.getsize(file_path)
            max_size_bytes = max_size_mb * 1024 * 1024
            
            if file_size > max_size_bytes:
                return False, f"File too large. Maximum size is {max_size_mb}MB"
            
            if file_size == 0:
                return False, "File is empty"
            
            # Check if file is readable
            try:
                with open(file_path, 'rb') as f:
                    f.read(1)
            except Exception:
                return False, "File is not readable"
            
            return True, "File is valid"
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
