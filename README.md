# Dr. Resume - AI-Powered Resume Scanner & Analysis Platform

![Dr. Resume Logo](https://img.shields.io/badge/Dr.%20Resume-AI%20Powered-blue?style=for-the-badge&logo=python)

**Dr. Resume** is a comprehensive, AI-powered resume scanning and analysis platform that helps job seekers optimize their resumes for specific job descriptions and improve ATS (Applicant Tracking System) compatibility.

## 🚀 Features Overview

This integrated application combines **US-01 through US-10** functionality into a single, powerful platform:

### Core Features
- **📝 Resume Upload & Processing** (US-03) - Support for PDF, DOC, DOCX files
- **💼 Job Description Management** (US-04) - Store and analyze job requirements
- **🔍 Keyword Extraction** (US-05) - AI-powered keyword parsing using NLTK/spaCy
- **📊 Matching Score Calculation** (US-06) - Advanced similarity algorithms
- **💡 Smart Suggestions** (US-07) - Basic and premium AI-powered recommendations
- **📈 Dashboard & Analytics** (US-08) - Comprehensive scan history and insights
- **🔐 User Authentication** (US-01, US-02) - Secure registration and JWT-based login
- **⚙️ Account Management** (US-10) - Profile settings and preferences
- **🛡️ API Protection** (US-09) - Role-based access control

### Premium Features
- **🤖 AI-Powered Suggestions** - OpenAI integration for advanced recommendations
- **📊 Advanced Analytics** - Detailed performance insights
- **🎯 Unlimited Scans** - No limits on resume analysis
- **⚡ Priority Support** - Enhanced customer support

## 🏗️ Architecture & Integration

### Technology Stack
- **Backend**: Flask, SQLAlchemy, JWT Authentication
- **Database**: SQLite (configurable to PostgreSQL)
- **Frontend**: HTML5, Bootstrap 5, Vanilla JavaScript
- **AI/NLP**: NLTK, scikit-learn, OpenAI API
- **File Processing**: PyPDF2, python-docx

### Integration Flow
```
US-01 (Registration) → US-02 (Login/JWT) → US-03 (Resume Upload) → US-04 (Job Description)
                                                    ↓
US-05 (Keyword Parsing) → US-06 (Matching Score) → US-07 (Suggestions) → US-08 (Dashboard)
                                                    ↓
                            US-09 (API Protection) → US-10 (Account Settings)
```

## 📦 Installation & Setup

### Prerequisites
- Python 3.9+
- pip (Python package manager)
- Git

### Quick Start

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd "docto resume"
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

4. **Initialize Database**
   ```bash
   python -c "from app import create_app; from models import db; app = create_app(); app.app_context().push(); db.create_all()"
   ```

5. **Run the Application**
   ```bash
   python app.py
   ```

6. **Access the Application**
   - Open your browser to: `http://localhost:5000`
   - API Documentation: `http://localhost:5000`
   - Health Check: `http://localhost:5000/health`

## ⚙️ Configuration

### Required Configuration (.env file)

| Variable | Description | Example |
|----------|-------------|---------|
| `SECRET_KEY` | Flask secret key | `your-secret-key-here` |
| `JWT_SECRET_KEY` | JWT token secret | `your-jwt-secret-here` |
| `DATABASE_URL` | Database connection | `sqlite:///dr_resume.db` |
| `UPLOAD_FOLDER` | File upload directory | `uploads` |
| `OPENAI_API_KEY` | OpenAI API key (premium) | `sk-...` |

### Optional Configuration

| Variable | Description | Default |
|----------|-------------|---------|
| `MAX_CONTENT_LENGTH` | Max file size (bytes) | `10485760` (10MB) |
| `FLASK_ENV` | Environment | `development` |
| `CORS_ORIGINS` | Allowed CORS origins | `http://localhost:5000` |

## 🔧 API Endpoints

### Authentication (US-01, US-02)
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `POST /api/refresh` - Refresh JWT token
- `GET /api/me` - Get current user

### Resume Management (US-03)
- `POST /api/upload_resume` - Upload resume file
- `GET /api/resumes` - List user's resumes
- `GET /api/resumes/<id>` - Get resume details
- `DELETE /api/resumes/<id>` - Delete resume

### Job Descriptions (US-04)
- `POST /api/upload_jd` - Create job description
- `GET /api/job_descriptions` - List job descriptions
- `PUT /api/job_descriptions/<id>` - Update job description

### Analysis (US-05, US-06, US-07)
- `POST /api/calculate_match` - Calculate matching score
- `GET /api/suggestions/<resume_id>/<jd_id>` - Get basic suggestions
- `POST /api/premium_suggestions` - Generate AI suggestions (Premium)

### Dashboard (US-08)
- `GET /api/history` - Get scan history
- `GET /api/analytics` - Get user analytics

### Account (US-10)
- `GET /api/account/profile` - Get user profile
- `PUT /api/account/profile` - Update profile
- `PUT /api/account/password` - Change password

## 📁 Project Structure

```
docto resume/
├── 📂 Individual US Folders (US-01 to US-10)
│   ├── US-01-User-Registration/
│   ├── US-02-Login-JWT-Token/
│   ├── US-03-Resume-Upload/
│   ├── US-04-JD-Upload/
│   ├── US-05-Keyword-Parsing/
│   ├── US-06-Matching-Score/
│   ├── US-07-Suggestions/
│   ├── US-08-Dashboard/
│   ├── US-09-API-Protection/
│   └── US-10-Account-Settings/
│
├── 🚀 INTEGRATED APPLICATION
│   ├── app.py                    # Main Flask application
│   ├── models.py                 # Database models (all US combined)
│   ├── requirements.txt          # Python dependencies
│   ├── .env.example             # Environment configuration template
│   │
│   ├── routes/                   # API endpoints
│   │   ├── auth_routes.py       # US-01, US-02: Authentication
│   │   ├── resume_routes.py     # US-03: Resume management
│   │   ├── job_description_routes.py # US-04: Job descriptions
│   │   ├── analysis_routes.py   # US-05, US-06, US-07: Analysis & suggestions
│   │   ├── dashboard_routes.py  # US-08: Dashboard & history
│   │   └── account_routes.py    # US-10: Account settings
│   │
│   ├── services/                # Business logic
│   │   ├── file_processor.py    # US-03: File processing
│   │   ├── keyword_extractor.py # US-05: Keyword extraction
│   │   ├── matching_engine.py   # US-06: Matching algorithms
│   │   └── suggestion_engine.py # US-07: Suggestion generation
│   │
│   ├── static/                  # Frontend files
│   │   ├── index.html          # Main UI (all US features)
│   │   └── js/app.js           # Frontend JavaScript
│   │
│   └── uploads/                # Uploaded resume files
```

## 🔄 Integration Workflow

### 1. User Registration & Authentication (US-01, US-02)
```python
# User registers → JWT token issued → Access to protected routes
POST /api/register → POST /api/login → Bearer token → Protected APIs
```

### 2. Resume & Job Description Upload (US-03, US-04)
```python
# File upload → Text extraction → Keyword parsing → Database storage
File Upload → PyPDF2/python-docx → NLTK/spaCy → SQLite/PostgreSQL
```

### 3. Analysis Pipeline (US-05, US-06, US-07)
```python
# Keywords → Matching calculation → Suggestions generation
Resume Keywords + JD Keywords → Jaccard/TF-IDF → Basic/AI Suggestions
```

### 4. Dashboard & History (US-08)
```python
# Scan results → Analytics → Dashboard display
Matching Scores → Statistics → Charts & Tables
```

## 🧪 Testing

### Manual Testing
1. **Registration**: Create a new account
2. **Login**: Authenticate with credentials
3. **Upload Resume**: Upload a PDF/DOC file
4. **Add Job Description**: Create a job posting
5. **Run Analysis**: Calculate matching score
6. **View Suggestions**: Get improvement recommendations
7. **Check Dashboard**: Review scan history

### API Testing
```bash
# Health check
curl http://localhost:5000/health

# Register user
curl -X POST http://localhost:5000/api/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!"}'

# Login
curl -X POST http://localhost:5000/api/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Test123!"}'
```

## 🚀 Deployment

### Development
```bash
python app.py
```

### Production (with Gunicorn)
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### Docker (Optional)
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "app.py"]
```

## 🔧 Database Migration (SQLite → PostgreSQL)

1. **Install PostgreSQL adapter**
   ```bash
   pip install psycopg2-binary
   ```

2. **Update .env file**
   ```env
   DATABASE_URL=postgresql://username:password@localhost:5432/dr_resume
   ```

3. **Create PostgreSQL database**
   ```sql
   CREATE DATABASE dr_resume;
   ```

4. **Run migration**
   ```python
   from app import create_app
   from models import db
   app = create_app()
   with app.app_context():
       db.create_all()
   ```

## 📊 Performance Optimization

### File Processing
- **Async Processing**: Implement Celery for background tasks
- **File Validation**: Client-side validation before upload
- **Compression**: Optimize file storage

### Database
- **Indexing**: Add indexes on frequently queried columns
- **Connection Pooling**: Use SQLAlchemy connection pooling
- **Query Optimization**: Optimize N+1 queries

### Caching
- **Redis**: Cache frequently accessed data
- **CDN**: Serve static files from CDN

## 🛡️ Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: Werkzeug password hashing
- **CORS Protection**: Configurable CORS origins
- **Rate Limiting**: API rate limiting
- **Input Validation**: Comprehensive input validation
- **File Type Validation**: Secure file upload validation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- 📧 Email: <EMAIL>
- 📖 Documentation: [docs.drresume.com](https://docs.drresume.com)
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/issues)

## 🎯 Roadmap

- [ ] Mobile app development
- [ ] Advanced AI models integration
- [ ] Multi-language support
- [ ] Resume templates
- [ ] Interview preparation features
- [ ] Job board integration
- [ ] Chrome extension
- [ ] Slack/Teams integration

---

**Built with ❤️ for job seekers worldwide**
