"""
Account Routes - US-10
======================

This module handles account settings and profile management.
Implements US-10 (Account Settings) functionality.

Routes:
- GET /api/account/profile - Get user profile
- PUT /api/account/profile - Update user profile
- PUT /api/account/password - Change password
- GET /api/account/preferences - Get user preferences
- PUT /api/account/preferences - Update user preferences
- GET /api/account/subscription - Get subscription info
"""

import json
from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.security import check_password_hash, generate_password_hash
from models import db, User, UserProfile

# Create Blueprint
account_bp = Blueprint('account', __name__, url_prefix='/api/account')

@account_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_user_profile():
    """
    Get User Profile Endpoint - US-10
    
    Returns complete user profile information
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find user and profile
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        # Get or create user profile
        user_profile = UserProfile.query.filter_by(user_id=current_user_id).first()
        
        if not user_profile:
            # Create default profile
            user_profile = UserProfile(user_id=current_user_id)
            db.session.add(user_profile)
            db.session.commit()
        
        # Combine user and profile data
        profile_data = {
            'user': user.to_dict(),
            'profile': user_profile.to_dict()
        }
        
        return jsonify({
            'success': True,
            'profile': profile_data
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get user profile: {str(e)}'
        }), 500

@account_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_user_profile():
    """
    Update User Profile Endpoint - US-10
    
    Updates user profile information
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find user and profile
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        user_profile = UserProfile.query.filter_by(user_id=current_user_id).first()
        
        if not user_profile:
            user_profile = UserProfile(user_id=current_user_id)
            db.session.add(user_profile)
        
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Update user basic info
        user_data = data.get('user', {})
        
        if 'first_name' in user_data:
            user.first_name = user_data['first_name'].strip() if user_data['first_name'] else None
        
        if 'last_name' in user_data:
            user.last_name = user_data['last_name'].strip() if user_data['last_name'] else None
        
        # Update profile info
        profile_data = data.get('profile', {})
        
        if 'display_name' in profile_data:
            user_profile.display_name = profile_data['display_name'].strip() if profile_data['display_name'] else None
        
        if 'bio' in profile_data:
            user_profile.bio = profile_data['bio'].strip() if profile_data['bio'] else None
        
        if 'phone_number' in profile_data:
            user_profile.phone_number = profile_data['phone_number'].strip() if profile_data['phone_number'] else None
        
        if 'country' in profile_data:
            country = profile_data['country'].strip() if profile_data['country'] else None
            if country and len(country) != 2:
                return jsonify({
                    'success': False,
                    'message': 'Country must be a 2-letter ISO code'
                }), 400
            user_profile.country = country
        
        if 'timezone' in profile_data:
            user_profile.timezone = profile_data['timezone'].strip() if profile_data['timezone'] else None
        
        if 'current_position' in profile_data:
            user_profile.current_position = profile_data['current_position'].strip() if profile_data['current_position'] else None
        
        if 'current_company' in profile_data:
            user_profile.current_company = profile_data['current_company'].strip() if profile_data['current_company'] else None
        
        if 'years_of_experience' in profile_data:
            years = profile_data['years_of_experience']
            if years is not None:
                try:
                    years = int(years)
                    if years < 0 or years > 50:
                        return jsonify({
                            'success': False,
                            'message': 'Years of experience must be between 0 and 50'
                        }), 400
                    user_profile.years_of_experience = years
                except (ValueError, TypeError):
                    return jsonify({
                        'success': False,
                        'message': 'Years of experience must be a number'
                    }), 400
        
        if 'industry' in profile_data:
            user_profile.industry = profile_data['industry'].strip() if profile_data['industry'] else None
        
        if 'skills' in profile_data:
            skills = profile_data['skills']
            if skills is not None:
                if isinstance(skills, list):
                    user_profile.skills = json.dumps(skills)
                else:
                    return jsonify({
                        'success': False,
                        'message': 'Skills must be an array'
                    }), 400
        
        if 'preferred_language' in profile_data:
            lang = profile_data['preferred_language'].strip() if profile_data['preferred_language'] else 'en'
            user_profile.preferred_language = lang
        
        # Update timestamps
        user.updated_at = datetime.utcnow()
        user_profile.updated_at = datetime.utcnow()
        user_profile.last_profile_update = datetime.utcnow()
        
        db.session.commit()
        
        # Return updated profile
        profile_data = {
            'user': user.to_dict(),
            'profile': user_profile.to_dict()
        }
        
        return jsonify({
            'success': True,
            'message': 'Profile updated successfully',
            'profile': profile_data
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to update profile: {str(e)}'
        }), 500

@account_bp.route('/password', methods=['PUT'])
@jwt_required()
def change_password():
    """
    Change Password Endpoint - US-10
    
    Changes user password with current password verification
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find user
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Extract passwords
        current_password = data.get('current_password', '')
        new_password = data.get('new_password', '')
        confirm_password = data.get('confirm_password', '')
        
        # Validate required fields
        if not current_password or not new_password or not confirm_password:
            return jsonify({
                'success': False,
                'message': 'Current password, new password, and confirmation are required'
            }), 400
        
        # Verify current password
        if not user.check_password(current_password):
            return jsonify({
                'success': False,
                'message': 'Current password is incorrect'
            }), 401
        
        # Validate new password
        if new_password != confirm_password:
            return jsonify({
                'success': False,
                'message': 'New password and confirmation do not match'
            }), 400
        
        # Validate password strength (same as registration)
        if len(new_password) < 8:
            return jsonify({
                'success': False,
                'message': 'New password must be at least 8 characters long'
            }), 400
        
        # Update password
        user.password_hash = generate_password_hash(new_password)
        user.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Password changed successfully'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to change password: {str(e)}'
        }), 500

@account_bp.route('/preferences', methods=['GET'])
@jwt_required()
def get_user_preferences():
    """
    Get User Preferences Endpoint - US-10
    
    Returns user preferences and settings
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find user profile
        user_profile = UserProfile.query.filter_by(user_id=current_user_id).first()
        
        if not user_profile:
            # Create default profile
            user_profile = UserProfile(user_id=current_user_id)
            db.session.add(user_profile)
            db.session.commit()
        
        # Extract preferences
        preferences = {
            'email_notifications': user_profile.email_notifications,
            'marketing_emails': user_profile.marketing_emails,
            'data_sharing': user_profile.data_sharing,
            'preferred_language': user_profile.preferred_language,
            'login_notifications': user_profile.login_notifications,
            'session_timeout_minutes': user_profile.session_timeout_minutes,
            'two_factor_enabled': user_profile.two_factor_enabled
        }
        
        return jsonify({
            'success': True,
            'preferences': preferences
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get preferences: {str(e)}'
        }), 500

@account_bp.route('/preferences', methods=['PUT'])
@jwt_required()
def update_user_preferences():
    """
    Update User Preferences Endpoint - US-10
    
    Updates user preferences and settings
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find user profile
        user_profile = UserProfile.query.filter_by(user_id=current_user_id).first()
        
        if not user_profile:
            user_profile = UserProfile(user_id=current_user_id)
            db.session.add(user_profile)
        
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Update preferences
        if 'email_notifications' in data:
            user_profile.email_notifications = bool(data['email_notifications'])
        
        if 'marketing_emails' in data:
            user_profile.marketing_emails = bool(data['marketing_emails'])
        
        if 'data_sharing' in data:
            user_profile.data_sharing = bool(data['data_sharing'])
        
        if 'preferred_language' in data:
            lang = data['preferred_language'].strip() if data['preferred_language'] else 'en'
            user_profile.preferred_language = lang
        
        if 'login_notifications' in data:
            user_profile.login_notifications = bool(data['login_notifications'])
        
        if 'session_timeout_minutes' in data:
            timeout = data['session_timeout_minutes']
            if timeout is not None:
                try:
                    timeout = int(timeout)
                    if timeout < 5 or timeout > 480:  # 5 minutes to 8 hours
                        return jsonify({
                            'success': False,
                            'message': 'Session timeout must be between 5 and 480 minutes'
                        }), 400
                    user_profile.session_timeout_minutes = timeout
                except (ValueError, TypeError):
                    return jsonify({
                        'success': False,
                        'message': 'Session timeout must be a number'
                    }), 400
        
        if 'two_factor_enabled' in data:
            user_profile.two_factor_enabled = bool(data['two_factor_enabled'])
        
        # Update timestamp
        user_profile.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        # Return updated preferences
        preferences = {
            'email_notifications': user_profile.email_notifications,
            'marketing_emails': user_profile.marketing_emails,
            'data_sharing': user_profile.data_sharing,
            'preferred_language': user_profile.preferred_language,
            'login_notifications': user_profile.login_notifications,
            'session_timeout_minutes': user_profile.session_timeout_minutes,
            'two_factor_enabled': user_profile.two_factor_enabled
        }
        
        return jsonify({
            'success': True,
            'message': 'Preferences updated successfully',
            'preferences': preferences
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to update preferences: {str(e)}'
        }), 500

@account_bp.route('/subscription', methods=['GET'])
@jwt_required()
def get_subscription_info():
    """
    Get Subscription Info Endpoint - US-10
    
    Returns user subscription and usage information
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find user and profile
        user = User.query.get(current_user_id)
        user_profile = UserProfile.query.filter_by(user_id=current_user_id).first()
        
        if not user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404
        
        if not user_profile:
            user_profile = UserProfile(user_id=current_user_id)
            db.session.add(user_profile)
            db.session.commit()
        
        # Prepare subscription info
        subscription_info = {
            'is_premium': user.is_premium,
            'subscription_status': user_profile.subscription_status,
            'subscription_start_date': user_profile.subscription_start_date.isoformat() if user_profile.subscription_start_date else None,
            'subscription_end_date': user_profile.subscription_end_date.isoformat() if user_profile.subscription_end_date else None,
            'billing_cycle': user_profile.billing_cycle,
            'usage_stats': {
                'total_scans': user_profile.total_scans,
                'premium_scans_used': user_profile.premium_scans_used,
                'premium_scans_limit': user_profile.premium_scans_limit,
                'api_calls_this_month': user_profile.api_calls_this_month
            },
            'features': {
                'basic_suggestions': True,
                'premium_ai_suggestions': user.is_premium,
                'unlimited_scans': user.is_premium,
                'advanced_analytics': user.is_premium,
                'priority_support': user.is_premium
            }
        }
        
        return jsonify({
            'success': True,
            'subscription': subscription_info
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get subscription info: {str(e)}'
        }), 500
