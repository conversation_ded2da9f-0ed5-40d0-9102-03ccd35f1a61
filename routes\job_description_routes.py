"""
Job Description Routes - US-04
==============================

This module handles job description upload and management.
Implements US-04 (JD Upload) functionality.

Routes:
- POST /api/upload_jd - Upload job description
- GET /api/job_descriptions - List user's job descriptions
- GET /api/job_descriptions/<id> - Get job description details
- PUT /api/job_descriptions/<id> - Update job description
- DELETE /api/job_descriptions/<id> - Delete job description
"""

from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, JobDescription, Keyword
from services.keyword_extractor import KeywordExtractor

# Create Blueprint
jd_bp = Blueprint('job_description', __name__, url_prefix='/api')

@jd_bp.route('/upload_jd', methods=['POST'])
@jwt_required()
def upload_job_description():
    """
    Job Description Upload Endpoint - US-04
    
    Expected JSON payload:
    {
        "title": "Software Engineer",
        "company_name": "Tech Corp",
        "job_description_text": "We are looking for...",
        "location": "San Francisco, CA",
        "employment_type": "full-time",
        "experience_level": "mid-level",
        "salary_range": "$80,000 - $120,000",
        "original_source": "LinkedIn",
        "job_url": "https://..."
    }
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Extract required fields
        title = data.get('title', '').strip()
        job_description_text = data.get('job_description_text', '').strip()
        
        # Validate required fields
        if not title or not job_description_text:
            return jsonify({
                'success': False,
                'message': 'Title and job description text are required'
            }), 400
        
        # Validate job description length
        if len(job_description_text) < 50:
            return jsonify({
                'success': False,
                'message': 'Job description must be at least 50 characters long'
            }), 400
        
        if len(job_description_text) > 50000:
            return jsonify({
                'success': False,
                'message': 'Job description must be less than 50,000 characters'
            }), 400
        
        # Extract optional fields
        company_name = data.get('company_name', '').strip()
        location = data.get('location', '').strip()
        employment_type = data.get('employment_type', 'full-time').strip()
        experience_level = data.get('experience_level', '').strip()
        salary_range = data.get('salary_range', '').strip()
        original_source = data.get('original_source', '').strip()
        job_url = data.get('job_url', '').strip()
        
        # Validate employment type
        valid_employment_types = ['full-time', 'part-time', 'contract', 'temporary', 'internship']
        if employment_type and employment_type not in valid_employment_types:
            return jsonify({
                'success': False,
                'message': f'Invalid employment type. Must be one of: {", ".join(valid_employment_types)}'
            }), 400
        
        # Validate experience level
        valid_experience_levels = ['entry-level', 'mid-level', 'senior-level', 'executive', 'not-specified']
        if experience_level and experience_level not in valid_experience_levels:
            return jsonify({
                'success': False,
                'message': f'Invalid experience level. Must be one of: {", ".join(valid_experience_levels)}'
            }), 400
        
        # Create job description record
        job_description = JobDescription(
            user_id=current_user_id,
            title=title,
            company_name=company_name or None,
            job_description_text=job_description_text,
            location=location or None,
            employment_type=employment_type,
            experience_level=experience_level or None,
            salary_range=salary_range or None,
            original_source=original_source or None,
            job_url=job_url or None
        )
        
        db.session.add(job_description)
        db.session.commit()
        
        # Extract keywords from job description
        try:
            keyword_extractor = KeywordExtractor()
            keywords = keyword_extractor.extract_keywords(job_description_text, 'job_description')
            
            # Save keywords to database
            for keyword_data in keywords:
                keyword = Keyword(
                    user_id=current_user_id,
                    job_description_id=job_description.id,
                    keyword=keyword_data['keyword'],
                    keyword_type=keyword_data.get('type', 'general'),
                    frequency=keyword_data.get('frequency', 1),
                    confidence_score=keyword_data.get('confidence', 0.8),
                    context_snippet=keyword_data.get('context', ''),
                    extraction_method='nltk_spacy'
                )
                db.session.add(keyword)
            
            # Update job description status
            job_description.is_processed = True
            job_description.keywords_extracted = True
            
            db.session.commit()
            
        except Exception as e:
            print(f"Warning: Keyword extraction failed: {e}")
            # Don't fail the entire request if keyword extraction fails
        
        return jsonify({
            'success': True,
            'message': 'Job description uploaded successfully',
            'job_description': job_description.to_dict()
        }), 201
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Upload failed: {str(e)}'
        }), 500

@jd_bp.route('/job_descriptions', methods=['GET'])
@jwt_required()
def list_job_descriptions():
    """
    List User's Job Descriptions Endpoint - US-04
    
    Returns all job descriptions for the current user
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        # Query job descriptions
        jd_query = JobDescription.query.filter_by(
            user_id=current_user_id
        ).order_by(JobDescription.created_at.desc())
        
        # Paginate results
        jd_paginated = jd_query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        return jsonify({
            'success': True,
            'job_descriptions': [jd.to_dict() for jd in jd_paginated.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': jd_paginated.total,
                'pages': jd_paginated.pages,
                'has_next': jd_paginated.has_next,
                'has_prev': jd_paginated.has_prev
            }
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to list job descriptions: {str(e)}'
        }), 500

@jd_bp.route('/job_descriptions/<jd_id>', methods=['GET'])
@jwt_required()
def get_job_description_details(jd_id):
    """
    Get Job Description Details Endpoint - US-04
    
    Returns detailed information about a specific job description
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find job description
        job_description = JobDescription.query.filter_by(
            id=jd_id,
            user_id=current_user_id
        ).first()
        
        if not job_description:
            return jsonify({
                'success': False,
                'message': 'Job description not found'
            }), 404
        
        # Get associated keywords
        keywords = Keyword.query.filter_by(job_description_id=jd_id).all()
        
        jd_data = job_description.to_dict()
        jd_data['keywords'] = [keyword.to_dict() for keyword in keywords]
        
        return jsonify({
            'success': True,
            'job_description': jd_data
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to get job description details: {str(e)}'
        }), 500

@jd_bp.route('/job_descriptions/<jd_id>', methods=['PUT'])
@jwt_required()
def update_job_description(jd_id):
    """
    Update Job Description Endpoint - US-04
    
    Updates an existing job description
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find job description
        job_description = JobDescription.query.filter_by(
            id=jd_id,
            user_id=current_user_id
        ).first()
        
        if not job_description:
            return jsonify({
                'success': False,
                'message': 'Job description not found'
            }), 404
        
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        # Update fields if provided
        if 'title' in data:
            title = data['title'].strip()
            if not title:
                return jsonify({
                    'success': False,
                    'message': 'Title cannot be empty'
                }), 400
            job_description.title = title
        
        if 'company_name' in data:
            job_description.company_name = data['company_name'].strip() or None
        
        if 'job_description_text' in data:
            job_text = data['job_description_text'].strip()
            if not job_text:
                return jsonify({
                    'success': False,
                    'message': 'Job description text cannot be empty'
                }), 400
            if len(job_text) < 50 or len(job_text) > 50000:
                return jsonify({
                    'success': False,
                    'message': 'Job description must be between 50 and 50,000 characters'
                }), 400
            
            job_description.job_description_text = job_text
            
            # Re-extract keywords if job description text changed
            try:
                # Delete existing keywords
                Keyword.query.filter_by(job_description_id=jd_id).delete()
                
                # Extract new keywords
                keyword_extractor = KeywordExtractor()
                keywords = keyword_extractor.extract_keywords(job_text, 'job_description')
                
                # Save new keywords
                for keyword_data in keywords:
                    keyword = Keyword(
                        user_id=current_user_id,
                        job_description_id=job_description.id,
                        keyword=keyword_data['keyword'],
                        keyword_type=keyword_data.get('type', 'general'),
                        frequency=keyword_data.get('frequency', 1),
                        confidence_score=keyword_data.get('confidence', 0.8),
                        context_snippet=keyword_data.get('context', ''),
                        extraction_method='nltk_spacy'
                    )
                    db.session.add(keyword)
                
                job_description.keywords_extracted = True
                
            except Exception as e:
                print(f"Warning: Keyword re-extraction failed: {e}")
        
        if 'location' in data:
            job_description.location = data['location'].strip() or None
        
        if 'employment_type' in data:
            employment_type = data['employment_type'].strip()
            valid_types = ['full-time', 'part-time', 'contract', 'temporary', 'internship']
            if employment_type and employment_type not in valid_types:
                return jsonify({
                    'success': False,
                    'message': f'Invalid employment type. Must be one of: {", ".join(valid_types)}'
                }), 400
            job_description.employment_type = employment_type
        
        if 'experience_level' in data:
            experience_level = data['experience_level'].strip()
            valid_levels = ['entry-level', 'mid-level', 'senior-level', 'executive', 'not-specified']
            if experience_level and experience_level not in valid_levels:
                return jsonify({
                    'success': False,
                    'message': f'Invalid experience level. Must be one of: {", ".join(valid_levels)}'
                }), 400
            job_description.experience_level = experience_level or None
        
        if 'salary_range' in data:
            job_description.salary_range = data['salary_range'].strip() or None
        
        if 'original_source' in data:
            job_description.original_source = data['original_source'].strip() or None
        
        if 'job_url' in data:
            job_description.job_url = data['job_url'].strip() or None
        
        # Update timestamp
        job_description.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Job description updated successfully',
            'job_description': job_description.to_dict()
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to update job description: {str(e)}'
        }), 500

@jd_bp.route('/job_descriptions/<jd_id>', methods=['DELETE'])
@jwt_required()
def delete_job_description(jd_id):
    """
    Delete Job Description Endpoint - US-04
    
    Deletes a job description and associated data
    """
    try:
        # Get current user
        current_user_id = get_jwt_identity()
        
        # Find job description
        job_description = JobDescription.query.filter_by(
            id=jd_id,
            user_id=current_user_id
        ).first()
        
        if not job_description:
            return jsonify({
                'success': False,
                'message': 'Job description not found'
            }), 404
        
        # Delete associated keywords (cascade should handle this, but being explicit)
        Keyword.query.filter_by(job_description_id=jd_id).delete()
        
        # Delete job description
        db.session.delete(job_description)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Job description deleted successfully'
        }), 200
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Failed to delete job description: {str(e)}'
        }), 500
