"""
Suggestion Engine Service - US-07
=================================

This service generates suggestions for resume improvement.
Implements US-07 (Suggestions) functionality with both basic and premium AI suggestions.

Tech Stack: OpenAI API (for premium), rule-based logic (for basic)
"""

import json
import logging
import os
from typing import List, Dict, Optional

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("Warning: OpenAI library not available. Premium suggestions will not work.")

from models import MatchingScore

class SuggestionEngine:
    """
    Engine for generating resume improvement suggestions
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize OpenAI if available
        if OPENAI_AVAILABLE:
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key:
                openai.api_key = api_key
            else:
                self.logger.warning("OpenAI API key not found. Premium suggestions will not work.")
    
    def generate_basic_suggestions(self, matching_score: MatchingScore) -> List[Dict]:
        """
        Generate basic suggestions based on matching score analysis
        
        Args:
            matching_score (MatchingScore): Calculated matching score
            
        Returns:
            List[Dict]: List of basic suggestions
        """
        try:
            suggestions = []
            
            # Parse keywords
            matched_keywords = json.loads(matching_score.matched_keywords) if matching_score.matched_keywords else []
            missing_keywords = json.loads(matching_score.missing_keywords) if matching_score.missing_keywords else []
            
            # Keyword-based suggestions
            if missing_keywords:
                suggestions.extend(self._generate_keyword_suggestions(missing_keywords))
            
            # Score-based suggestions
            if matching_score.overall_match_percentage < 50:
                suggestions.extend(self._generate_low_score_suggestions(matching_score))
            elif matching_score.overall_match_percentage < 75:
                suggestions.extend(self._generate_medium_score_suggestions(matching_score))
            
            # Category-specific suggestions
            if matching_score.skill_match_percentage < 60:
                suggestions.extend(self._generate_skill_suggestions())
            
            if matching_score.experience_match_percentage < 60:
                suggestions.extend(self._generate_experience_suggestions())
            
            if matching_score.education_match_percentage < 60:
                suggestions.extend(self._generate_education_suggestions())
            
            # Limit to top 10 suggestions
            return suggestions[:10]
            
        except Exception as e:
            self.logger.error(f"Error generating basic suggestions: {e}")
            return []
    
    def generate_premium_suggestions(self, matching_score: MatchingScore, 
                                   focus_areas: List[str] = None, 
                                   detail_level: str = 'detailed') -> Optional[List[Dict]]:
        """
        Generate premium AI-powered suggestions using OpenAI
        
        Args:
            matching_score (MatchingScore): Calculated matching score
            focus_areas (List[str]): Areas to focus on
            detail_level (str): Level of detail for suggestions
            
        Returns:
            Optional[List[Dict]]: List of premium suggestions or None if failed
        """
        try:
            if not OPENAI_AVAILABLE or not openai.api_key:
                self.logger.error("OpenAI not available for premium suggestions")
                return None
            
            # Prepare context for AI
            context = self._prepare_ai_context(matching_score, focus_areas)
            
            # Generate AI suggestions
            ai_response = self._call_openai_api(context, detail_level)
            
            if not ai_response:
                return None
            
            # Parse and format AI response
            suggestions = self._parse_ai_response(ai_response)
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"Error generating premium suggestions: {e}")
            return None
    
    def _generate_keyword_suggestions(self, missing_keywords: List[str]) -> List[Dict]:
        """Generate suggestions for missing keywords"""
        suggestions = []
        
        if len(missing_keywords) > 0:
            # Group keywords by importance
            high_priority_keywords = missing_keywords[:5]
            
            suggestions.append({
                'category': 'keywords',
                'title': 'Add Missing Keywords',
                'description': f'Your resume is missing {len(missing_keywords)} important keywords from the job description. Consider incorporating these key terms: {", ".join(high_priority_keywords[:3])}.',
                'priority': 'high',
                'implementation_steps': [
                    'Review the job description carefully',
                    'Identify where these keywords naturally fit in your experience',
                    'Update your resume sections to include relevant keywords',
                    'Ensure keywords are used in context, not just listed'
                ],
                'examples': [
                    f'If the job requires "{high_priority_keywords[0]}", add it to your skills or experience sections',
                    'Use keywords in bullet points describing your achievements',
                    'Include keywords in your summary or objective statement'
                ]
            })
        
        return suggestions
    
    def _generate_low_score_suggestions(self, matching_score: MatchingScore) -> List[Dict]:
        """Generate suggestions for low matching scores"""
        suggestions = []
        
        suggestions.append({
            'category': 'overall',
            'title': 'Significant Resume Improvements Needed',
            'description': f'Your resume has a {matching_score.overall_match_percentage:.1f}% match with this job. This indicates major gaps that need attention.',
            'priority': 'critical',
            'implementation_steps': [
                'Carefully review the job requirements',
                'Identify your relevant experience that matches the role',
                'Rewrite your resume to highlight matching qualifications',
                'Consider gaining additional skills if needed'
            ],
            'examples': [
                'Tailor your professional summary to match the job',
                'Reorganize your experience to highlight relevant roles',
                'Add a skills section with job-relevant technologies'
            ]
        })
        
        return suggestions
    
    def _generate_medium_score_suggestions(self, matching_score: MatchingScore) -> List[Dict]:
        """Generate suggestions for medium matching scores"""
        suggestions = []
        
        suggestions.append({
            'category': 'optimization',
            'title': 'Fine-tune Your Resume',
            'description': f'Your resume has a {matching_score.overall_match_percentage:.1f}% match. With some targeted improvements, you can significantly increase your chances.',
            'priority': 'medium',
            'implementation_steps': [
                'Add missing keywords naturally throughout your resume',
                'Quantify your achievements with specific metrics',
                'Highlight relevant projects and accomplishments',
                'Optimize your resume format for ATS systems'
            ],
            'examples': [
                'Use action verbs that match the job description',
                'Include specific technologies and tools mentioned in the job',
                'Add relevant certifications or training'
            ]
        })
        
        return suggestions
    
    def _generate_skill_suggestions(self) -> List[Dict]:
        """Generate skill-related suggestions"""
        return [{
            'category': 'skills',
            'title': 'Enhance Your Skills Section',
            'description': 'Your skills don\'t strongly align with the job requirements. Consider highlighting more relevant technical and soft skills.',
            'priority': 'medium',
            'implementation_steps': [
                'Create a dedicated skills section',
                'List technical skills that match the job',
                'Include relevant soft skills',
                'Organize skills by category (technical, leadership, etc.)'
            ],
            'examples': [
                'Add programming languages mentioned in the job',
                'Include relevant software and tools',
                'Highlight industry-specific skills'
            ]
        }]
    
    def _generate_experience_suggestions(self) -> List[Dict]:
        """Generate experience-related suggestions"""
        return [{
            'category': 'experience',
            'title': 'Better Highlight Relevant Experience',
            'description': 'Your work experience could be better aligned with the job requirements. Focus on relevant roles and achievements.',
            'priority': 'medium',
            'implementation_steps': [
                'Prioritize relevant work experience',
                'Use bullet points that match job requirements',
                'Quantify your achievements with numbers',
                'Include relevant projects and accomplishments'
            ],
            'examples': [
                'Lead with your most relevant job titles',
                'Use metrics to show impact (e.g., "Increased efficiency by 25%")',
                'Include relevant volunteer work or side projects'
            ]
        }]
    
    def _generate_education_suggestions(self) -> List[Dict]:
        """Generate education-related suggestions"""
        return [{
            'category': 'education',
            'title': 'Strengthen Your Educational Background',
            'description': 'Your educational background could be better presented to match the job requirements.',
            'priority': 'low',
            'implementation_steps': [
                'Highlight relevant coursework',
                'Include relevant certifications',
                'Add continuing education or training',
                'Mention relevant academic projects'
            ],
            'examples': [
                'List relevant courses that match job requirements',
                'Include professional certifications',
                'Add online courses or bootcamps'
            ]
        }]
    
    def _prepare_ai_context(self, matching_score: MatchingScore, focus_areas: List[str]) -> str:
        """Prepare context for AI API call"""
        matched_keywords = json.loads(matching_score.matched_keywords) if matching_score.matched_keywords else []
        missing_keywords = json.loads(matching_score.missing_keywords) if matching_score.missing_keywords else []
        
        context = f"""
        Resume Analysis Results:
        - Overall Match: {matching_score.overall_match_percentage:.1f}%
        - Skills Match: {matching_score.skill_match_percentage:.1f}%
        - Experience Match: {matching_score.experience_match_percentage:.1f}%
        - Education Match: {matching_score.education_match_percentage:.1f}%
        
        Matched Keywords: {', '.join(matched_keywords[:10])}
        Missing Keywords: {', '.join(missing_keywords[:10])}
        
        Focus Areas: {', '.join(focus_areas) if focus_areas else 'All areas'}
        
        Please provide specific, actionable suggestions to improve this resume's match with the job description.
        """
        
        return context
    
    def _call_openai_api(self, context: str, detail_level: str) -> Optional[str]:
        """Call OpenAI API to generate suggestions"""
        try:
            prompt = f"""
            You are an expert resume consultant. Based on the following resume analysis, provide specific, actionable suggestions to improve the resume's match with the job description.
            
            {context}
            
            Provide suggestions in the following JSON format:
            [
                {{
                    "category": "keywords|skills|experience|education|format",
                    "title": "Brief suggestion title",
                    "description": "Detailed explanation of the suggestion",
                    "priority": "low|medium|high|critical",
                    "implementation_steps": ["step1", "step2", "step3"],
                    "examples": ["example1", "example2"]
                }}
            ]
            
            Detail level: {detail_level}
            Provide 5-8 suggestions maximum.
            """
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert resume consultant providing specific, actionable advice."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            self.logger.error(f"OpenAI API call failed: {e}")
            return None
    
    def _parse_ai_response(self, ai_response: str) -> List[Dict]:
        """Parse AI response into structured suggestions"""
        try:
            # Try to extract JSON from the response
            start_idx = ai_response.find('[')
            end_idx = ai_response.rfind(']') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = ai_response[start_idx:end_idx]
                suggestions = json.loads(json_str)
                
                # Add AI metadata to each suggestion
                for suggestion in suggestions:
                    suggestion['ai_model_used'] = 'gpt-3.5-turbo'
                    suggestion['tokens_used'] = len(ai_response.split()) // len(suggestions)  # Rough estimate
                    suggestion['cost'] = 0.002 * (suggestion['tokens_used'] / 1000)  # Rough cost estimate
                
                return suggestions
            else:
                self.logger.error("Could not find JSON in AI response")
                return []
                
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse AI response as JSON: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Error parsing AI response: {e}")
            return []
