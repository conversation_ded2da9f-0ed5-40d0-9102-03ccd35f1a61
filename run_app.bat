@echo off
REM Dr. Resume - Windows Startup Script
REM ===================================
REM 
REM This batch file provides an easy way to start the Dr. Resume application
REM on Windows systems with automatic dependency installation and setup.

title Dr. Resume - AI-Powered Resume Scanner

echo.
echo ================================================================
echo  Dr. Resume - AI-Powered Resume Scanner
echo ================================================================
echo  Starting application setup and initialization...
echo ================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python is not installed or not in PATH
    echo    Please install Python 3.9+ from https://python.org
    echo    Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo ✅ Python is installed
python --version

REM Check if pip is available
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: pip is not available
    echo    Please ensure pip is installed with Python
    pause
    exit /b 1
)

echo ✅ pip is available

REM Check if requirements.txt exists
if not exist requirements.txt (
    echo ❌ Error: requirements.txt not found
    echo    Please ensure you're running this script from the project directory
    pause
    exit /b 1
)

echo ✅ requirements.txt found

REM Install dependencies
echo.
echo 📦 Installing Python dependencies...
echo    This may take a few minutes on first run...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Error: Failed to install dependencies
    echo    Please check your internet connection and try again
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully

REM Create .env file if it doesn't exist
if not exist .env (
    if exist .env.example (
        echo.
        echo 📝 Creating .env file from template...
        copy .env.example .env >nul
        echo ✅ .env file created
        echo ⚠️  Please edit .env file with your configuration before running again
        echo    Especially set your OpenAI API key for premium features
        pause
        exit /b 0
    ) else (
        echo ⚠️  No .env file found, using default configuration
    )
) else (
    echo ✅ .env file exists
)

REM Create uploads directory
if not exist uploads (
    mkdir uploads
    echo ✅ Created uploads directory
) else (
    echo ✅ uploads directory exists
)

REM Start the application
echo.
echo 🚀 Starting Dr. Resume application...
echo.
echo ================================================================
echo  Application Information:
echo ================================================================
echo  🌐 Web Interface: http://localhost:5000
echo  📚 API Documentation: http://localhost:5000
echo  🔍 Health Check: http://localhost:5000/health
echo  🎯 Main Application: http://localhost:5000/static/index.html
echo ================================================================
echo.
echo 💡 Quick Start Guide:
echo    1. Open http://localhost:5000 in your web browser
echo    2. Register a new account or login
echo    3. Upload your resume (PDF, DOC, or DOCX)
echo    4. Add a job description you're interested in
echo    5. Click "Run Analysis" to get your matching score
echo    6. Review suggestions to improve your resume
echo.
echo 🛑 Press Ctrl+C to stop the server when done
echo ================================================================
echo.

REM Run the application
python run_app.py --env development

REM If we get here, the application has stopped
echo.
echo 🛑 Dr. Resume application has stopped
echo.
pause
