#!/usr/bin/env python3
"""
Dr. Resume - Application Startup Script
======================================

This script provides an easy way to start the Dr. Resume application
with proper initialization and error handling.

Usage:
    python run_app.py [--env development|production|testing]
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 9):
        print("❌ Error: Python 3.9 or higher is required")
        print(f"   Current version: {sys.version}")
        sys.exit(1)
    print(f"✅ Python version: {sys.version.split()[0]}")

def check_dependencies():
    """Check if required dependencies are installed"""
    try:
        import flask
        print(f"✅ Flask version: {flask.__version__}")
    except ImportError:
        print("❌ Flask not installed. Installing dependencies...")
        install_dependencies()

def install_dependencies():
    """Install required dependencies"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        sys.exit(1)
    
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        sys.exit(1)

def setup_environment(env_name):
    """Setup environment variables"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("📝 Creating .env file from template...")
            import shutil
            shutil.copy(env_example, env_file)
            print("✅ .env file created. Please update it with your configuration.")
        else:
            print("⚠️  No .env file found. Using default configuration.")
    
    # Set environment variables
    os.environ['FLASK_ENV'] = env_name
    os.environ['FLASK_DEBUG'] = 'True' if env_name == 'development' else 'False'
    
    print(f"✅ Environment: {env_name}")

def create_upload_directory():
    """Create upload directory if it doesn't exist"""
    upload_dir = Path("uploads")
    upload_dir.mkdir(exist_ok=True)
    print(f"✅ Upload directory: {upload_dir.absolute()}")

def initialize_database():
    """Initialize the database"""
    try:
        from app import create_app
        from models import db
        
        app = create_app()
        with app.app_context():
            db.create_all()
            print("✅ Database initialized")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        sys.exit(1)

def download_nltk_data():
    """Download required NLTK data"""
    try:
        import nltk
        
        # Download required NLTK data
        nltk_downloads = [
            'punkt',
            'stopwords',
            'averaged_perceptron_tagger',
            'wordnet',
            'maxent_ne_chunker',
            'words'
        ]
        
        print("📚 Downloading NLTK data...")
        for item in nltk_downloads:
            try:
                nltk.data.find(f'tokenizers/{item}')
            except LookupError:
                try:
                    nltk.data.find(f'corpora/{item}')
                except LookupError:
                    try:
                        nltk.data.find(f'taggers/{item}')
                    except LookupError:
                        try:
                            nltk.data.find(f'chunkers/{item}')
                        except LookupError:
                            nltk.download(item, quiet=True)
        
        print("✅ NLTK data ready")
    except ImportError:
        print("⚠️  NLTK not available. Some features may not work.")
    except Exception as e:
        print(f"⚠️  NLTK data download failed: {e}")

def print_startup_info():
    """Print application startup information"""
    print("\n" + "="*60)
    print("🚀 Dr. Resume - AI-Powered Resume Scanner")
    print("="*60)
    print("📋 Features: US-01 to US-10 (Registration → Premium Suggestions)")
    print("🌐 Server: http://localhost:5000")
    print("📚 API Documentation: http://localhost:5000")
    print("🔍 Health Check: http://localhost:5000/health")
    print("🎯 Frontend: http://localhost:5000/static/index.html")
    print("="*60)
    print("\n💡 Quick Start:")
    print("   1. Open http://localhost:5000 in your browser")
    print("   2. Register a new account")
    print("   3. Upload a resume (PDF/DOC/DOCX)")
    print("   4. Add a job description")
    print("   5. Run analysis to get matching score")
    print("   6. View suggestions for improvement")
    print("\n🛑 Press Ctrl+C to stop the server")
    print("="*60)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Dr. Resume Application Startup Script')
    parser.add_argument(
        '--env',
        choices=['development', 'production', 'testing'],
        default='development',
        help='Environment to run in (default: development)'
    )
    parser.add_argument(
        '--host',
        default='0.0.0.0',
        help='Host to bind to (default: 0.0.0.0)'
    )
    parser.add_argument(
        '--port',
        type=int,
        default=5000,
        help='Port to bind to (default: 5000)'
    )
    parser.add_argument(
        '--skip-deps',
        action='store_true',
        help='Skip dependency installation'
    )
    
    args = parser.parse_args()
    
    print("🔧 Initializing Dr. Resume Application...")
    
    # Check Python version
    check_python_version()
    
    # Install dependencies if needed
    if not args.skip_deps:
        check_dependencies()
    
    # Setup environment
    setup_environment(args.env)
    
    # Create necessary directories
    create_upload_directory()
    
    # Initialize database
    initialize_database()
    
    # Download NLTK data
    download_nltk_data()
    
    # Print startup information
    print_startup_info()
    
    # Start the application
    try:
        from app import create_app
        app = create_app(args.env)
        
        app.run(
            host=args.host,
            port=args.port,
            debug=(args.env == 'development'),
            use_reloader=(args.env == 'development')
        )
    except KeyboardInterrupt:
        print("\n\n🛑 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Application failed to start: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
