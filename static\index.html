<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dr. Resume - AI-Powered Resume Scanner</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        
        body {
            background: var(--light-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            transition: color 0.3s ease;
        }
        
        .nav-link:hover, .nav-link.active {
            color: white !important;
        }
        
        .main-content {
            padding: 20px 0;
            min-height: calc(100vh - 76px);
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: var(--primary-color);
            background-color: rgba(102, 126, 234, 0.05);
        }
        
        .upload-area.dragover {
            border-color: var(--primary-color);
            background-color: rgba(102, 126, 234, 0.1);
        }
        
        .progress-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            margin: 0 auto;
        }
        
        .score-excellent { background: linear-gradient(135deg, #28a745, #20c997); }
        .score-good { background: linear-gradient(135deg, #17a2b8, #20c997); }
        .score-fair { background: linear-gradient(135deg, #ffc107, #fd7e14); }
        .score-poor { background: linear-gradient(135deg, #dc3545, #e83e8c); }
        
        .keyword-tag {
            display: inline-block;
            background: var(--primary-color);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            margin: 2px;
        }
        
        .keyword-tag.missing {
            background: var(--danger-color);
        }
        
        .keyword-tag.matched {
            background: var(--success-color);
        }
        
        .suggestion-card {
            border-left: 4px solid var(--primary-color);
        }
        
        .suggestion-card.priority-high {
            border-left-color: var(--danger-color);
        }
        
        .suggestion-card.priority-medium {
            border-left-color: var(--warning-color);
        }
        
        .suggestion-card.priority-low {
            border-left-color: var(--info-color);
        }
        
        .loading-spinner {
            display: none;
        }
        
        .loading-spinner.show {
            display: inline-block;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .badge {
            border-radius: 20px;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e1e5e9;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .section {
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .premium-badge {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .main-content {
                padding: 10px 0;
            }
            
            .card {
                margin-bottom: 20px;
            }
            
            .progress-circle {
                width: 100px;
                height: 100px;
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#" onclick="showSection('dashboard')">
                <i class="fas fa-file-alt me-2"></i>Dr. Resume
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('dashboard')">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('upload')">
                            <i class="fas fa-upload me-1"></i>Upload Resume
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('jobs')">
                            <i class="fas fa-briefcase me-1"></i>Job Descriptions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('analysis')">
                            <i class="fas fa-chart-line me-1"></i>Analysis
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showSection('history')">
                            <i class="fas fa-history me-1"></i>History
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown" id="userDropdown" style="display: none;">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><span id="userName">User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showSection('account')">
                                <i class="fas fa-cog me-2"></i>Account Settings
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item" id="loginButton">
                        <a class="nav-link" href="#" onclick="showSection('login')">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container main-content">
        <!-- Login Section -->
        <div id="loginSection" class="section active">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header text-center">
                            <h3><i class="fas fa-sign-in-alt me-2"></i>Login to Dr. Resume</h3>
                        </div>
                        <div class="card-body">
                            <form id="loginForm">
                                <div class="mb-3">
                                    <label for="loginEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="loginEmail" required>
                                </div>
                                <div class="mb-3">
                                    <label for="loginPassword" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="loginPassword" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <span class="loading-spinner spinner-border spinner-border-sm me-2"></span>
                                    Login
                                </button>
                            </form>
                            <div class="text-center mt-3">
                                <p>Don't have an account? <a href="#" onclick="showSection('register')">Register here</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Register Section -->
        <div id="registerSection" class="section">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header text-center">
                            <h3><i class="fas fa-user-plus me-2"></i>Create Account</h3>
                        </div>
                        <div class="card-body">
                            <form id="registerForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="registerFirstName" class="form-label">First Name</label>
                                        <input type="text" class="form-control" id="registerFirstName">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="registerLastName" class="form-label">Last Name</label>
                                        <input type="text" class="form-control" id="registerLastName">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="registerEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="registerEmail" required>
                                </div>
                                <div class="mb-3">
                                    <label for="registerPassword" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="registerPassword" required>
                                    <div class="form-text">Password must be at least 8 characters with uppercase, lowercase, and number.</div>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <span class="loading-spinner spinner-border spinner-border-sm me-2"></span>
                                    Create Account
                                </button>
                            </form>
                            <div class="text-center mt-3">
                                <p>Already have an account? <a href="#" onclick="showSection('login')">Login here</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboardSection" class="section">
            <div class="row">
                <div class="col-12">
                    <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h2>
                </div>
            </div>
            
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3 mb-3">
                    <div class="card stats-card">
                        <div class="card-body text-center">
                            <i class="fas fa-file-alt fa-2x mb-2"></i>
                            <div class="stats-number" id="totalResumes">0</div>
                            <div>Resumes</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stats-card">
                        <div class="card-body text-center">
                            <i class="fas fa-briefcase fa-2x mb-2"></i>
                            <div class="stats-number" id="totalJobs">0</div>
                            <div>Job Descriptions</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stats-card">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-line fa-2x mb-2"></i>
                            <div class="stats-number" id="totalScans">0</div>
                            <div>Scans</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card stats-card">
                        <div class="card-body text-center">
                            <i class="fas fa-percentage fa-2x mb-2"></i>
                            <div class="stats-number" id="avgScore">0%</div>
                            <div>Avg. Match</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-history me-2"></i>Recent Scans</h5>
                        </div>
                        <div class="card-body">
                            <div id="recentScans">
                                <p class="text-muted">No recent scans found.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-pie me-2"></i>Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="showSection('upload')">
                                    <i class="fas fa-upload me-2"></i>Upload Resume
                                </button>
                                <button class="btn btn-outline-primary" onclick="showSection('jobs')">
                                    <i class="fas fa-briefcase me-2"></i>Add Job Description
                                </button>
                                <button class="btn btn-outline-primary" onclick="showSection('analysis')">
                                    <i class="fas fa-chart-line me-2"></i>Run Analysis
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Section -->
        <div id="uploadSection" class="section">
            <div class="row">
                <div class="col-12">
                    <h2><i class="fas fa-upload me-2"></i>Upload Resume</h2>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5>Upload Your Resume</h5>
                        </div>
                        <div class="card-body">
                            <form id="resumeUploadForm">
                                <div class="upload-area" id="uploadArea">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <h5>Drag & Drop your resume here</h5>
                                    <p class="text-muted">or click to browse files</p>
                                    <input type="file" id="resumeFile" accept=".pdf,.doc,.docx" style="display: none;">
                                    <button type="button" class="btn btn-primary" onclick="document.getElementById('resumeFile').click()">
                                        Choose File
                                    </button>
                                </div>
                                
                                <div class="mt-3">
                                    <label for="resumeTitle" class="form-label">Resume Title (Optional)</label>
                                    <input type="text" class="form-control" id="resumeTitle" placeholder="e.g., Software Engineer Resume">
                                </div>
                                
                                <div class="mt-3">
                                    <label for="resumeDescription" class="form-label">Description (Optional)</label>
                                    <textarea class="form-control" id="resumeDescription" rows="3" placeholder="Brief description of this resume version"></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-primary mt-3">
                                    <span class="loading-spinner spinner-border spinner-border-sm me-2"></span>
                                    Upload Resume
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle me-2"></i>Upload Guidelines</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Supported formats: PDF, DOC, DOCX</li>
                                <li><i class="fas fa-check text-success me-2"></i>Maximum file size: 10MB</li>
                                <li><i class="fas fa-check text-success me-2"></i>Text will be automatically extracted</li>
                                <li><i class="fas fa-check text-success me-2"></i>Keywords will be parsed using AI</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>Your Resumes</h5>
                        </div>
                        <div class="card-body">
                            <div id="resumeList">
                                <p class="text-muted">No resumes uploaded yet.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alert Container -->
        <div id="alertContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1050;"></div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/app.js"></script>
</body>
</html>
